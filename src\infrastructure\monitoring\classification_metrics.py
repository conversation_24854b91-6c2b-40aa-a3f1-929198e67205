"""Metrics collection implementation for classification orchestrator performance monitoring."""

import logging
from typing import Dict, Any, List

from src.domain.interfaces.metrics_interface import MetricsInterface
from src.domain.entities.classification_result import ClassificationResult
from src.domain.entities.confidence_level import ConfidenceLevel

logger = logging.getLogger(__name__)


class ClassificationMetrics(MetricsInterface):
    """Metrics collection for classification orchestrator performance monitoring."""

    def __init__(self):
        self.total_processed = 0
        self.successful_classifications = 0
        self.high_confidence_results = 0
        self.processing_times = []
        self.intent_detection_times = []
        self.rag_search_times = []
        self.llm_classification_times = []

    def record_successful_classification(
        self,
        result: ClassificationResult,
        intent_time: int,
        rag_time: int,
        llm_time: int,
    ) -> None:
        """Record metrics for a successful classification."""
        self.total_processed += 1
        self.successful_classifications += 1

        if result.confidence == ConfidenceLevel.HIGH:
            self.high_confidence_results += 1

        total_time = result.processing_time_ms or 0
        self.processing_times.append(total_time)
        self.intent_detection_times.append(intent_time)
        self.rag_search_times.append(rag_time)
        self.llm_classification_times.append(llm_time)

        logger.debug(
            f"Recorded successful classification: {result.sub_category} "
            f"(confidence: {result.confidence}, time: {total_time}ms)"
        )

    def record_failed_classification(self) -> None:
        """Record a failed classification."""
        self.total_processed += 1
        logger.debug("Recorded failed classification")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        if self.total_processed == 0:
            return {
                "total_processed": 0,
                "successful_classifications": 0,
                "success_rate": 0.0,
                "high_confidence_rate": 0.0,
                "avg_processing_time_ms": 0.0,
                "avg_intent_detection_time_ms": 0.0,
                "avg_rag_search_time_ms": 0.0,
                "avg_llm_classification_time_ms": 0.0,
            }

        success_rate = self.successful_classifications / self.total_processed
        high_confidence_rate = (
            self.high_confidence_results / self.successful_classifications
            if self.successful_classifications > 0
            else 0.0
        )

        return {
            "total_processed": self.total_processed,
            "successful_classifications": self.successful_classifications,
            "success_rate": round(success_rate, 3),
            "high_confidence_rate": round(high_confidence_rate, 3),
            "avg_processing_time_ms": round(self._safe_avg(self.processing_times), 2),
            "avg_intent_detection_time_ms": round(self._safe_avg(self.intent_detection_times), 2),
            "avg_rag_search_time_ms": round(self._safe_avg(self.rag_search_times), 2),
            "avg_llm_classification_time_ms": round(self._safe_avg(self.llm_classification_times), 2),
        }

    def _safe_avg(self, values: List[int]) -> float:
        """Calculate average safely, returning 0.0 for empty lists."""
        return sum(values) / len(values) if values else 0.0