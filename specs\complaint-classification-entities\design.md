# Technical Design: Complaint Classification Entities

## Overview

This document provides comprehensive technical implementation specifications for the complaint classification entities feature. These entities form the core domain models for processing Taiwan government citizen complaints through automated classification using AI-powered semantic analysis.

## Architecture Integration

### Clean Architecture Layer Placement

```
src/domain/entities/
├── complaint_input.py          # New: Input validation entity
├── classification_result.py    # New: Output classification entity
├── intent_type.py              # New: Intent enumeration
├── main_category.py            # Existing: Reused without modification
└── sub_category.py             # Existing: Reused without modification
```

### Dependencies and Integration Points

**Domain Layer Integration:**

-   Follows existing Pydantic BaseModel patterns from `MainCategory` and `SubCategory`
-   Maintains consistent datetime serialization using `field_serializer`
-   Integrates with existing entity validation patterns

**Infrastructure Layer Integration:**

-   Compatible with existing `GeminiEmbedder` for embedding generation
-   Works with existing `ElasticsearchRepository` for similarity search
-   Supports existing configuration management via `Settings` class

## Entity Specifications

### 1. ComplaintInput Entity

**File:** `src/domain/entities/complaint_input.py`

```python
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator

class ComplaintInput(BaseModel):
    """
    Represents citizen complaint input for classification processing.

    This entity validates and structures incoming complaint data before
    classification processing begins.
    """

    case_id: str = Field(
        ...,
        description="唯一投訴識別碼",
        min_length=1,
        max_length=100
    )

    subject: str = Field(
        ...,
        description="投訴標題或主旨",
        min_length=1,
        max_length=200
    )

    content: str = Field(
        ...,
        description="投訴內容文本",
        min_length=10,
        max_length=10000
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="額外的元數據字段用於處理上下文"
    )

    record_time: datetime = Field(
        default_factory=datetime.now,
        description="接收投訴的時間戳"
    )

    @field_validator('subject')
    @classmethod
    def validate_subject(cls, v: str) -> str:
        """驗證投訴標題是否符合要求"""
        if not v or len(v.strip()) < 1:
            raise ValueError("投訴標題不能為空")
        return v.strip()

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """驗證投訴內容是否符合最低要求"""
        if not v or len(v.strip()) < 10:
            raise ValueError("投訴內容必須至少包含10個字符")
        return v.strip()

    @field_validator('case_id')
    @classmethod
    def validate_case_id(cls, v: str) -> str:
        """驗證投訴ID格式"""
        if not v or not v.strip():
            raise ValueError("投訴ID不能為空")
        return v.strip()

    @field_serializer('record_time')
    def serialize_record_time(self, value: datetime) -> str:
        """將record_time格式化為Elasticsearch所需的yyyy/MM/dd HH:mm:ss格式"""
        return value.strftime('%Y/%m/%d %H:%M:%S')
```

### 2. IntentType Enumeration

**File:** `src/domain/entities/intent_type.py`

```python
from enum import Enum

class IntentType(str, Enum):
    """
    Taiwan government complaint intent types enumeration.

    Defines all possible intent categories that can be detected
    in citizen complaints based on Taiwan government classification.
    """

    REPORT_VIOLATION = "檢舉告發"
    REQUEST_ASSISTANCE = "請求協助"
    COMPLAINT_PROBLEM = "抱怨問題"
    REQUEST_IMPROVEMENT = "請求改善"
    CONSULTATION_INQUIRY = "諮詢問題"
    DISSATISFIED_SERVICE_ATTITUDE = "不滿服務態度"
    DISSATISFIED_STAFF_PROFESSIONALISM = "不滿人員專業度"
    GRATITUDE_PRAISE = "感謝讚美"
    SUGGEST_PROPOSAL = "提出建議"

    @classmethod
    def get_all_intents(cls) -> list[str]:
        """返回所有可用意圖類型的列表"""
        return [intent.value for intent in cls]

    @classmethod
    def is_valid_intent(cls, intent: str) -> bool:
        """檢查給定字符串是否為有效意圖類型"""
        return intent in cls.get_all_intents()
```

### 3. ConfidenceLevel Enumeration

**File:** `src/domain/entities/confidence_level.py`

```python
from enum import Enum

class ConfidenceLevel(str, Enum):
    """
    Classification confidence level enumeration.

    Defines confidence levels for complaint classification results
    based on similarity scores and classification certainty.
    """

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
```

### 4. RAGCandidate Entity

**File:** `src/domain/entities/rag_candidate.py`

```python
from typing import List
from pydantic import BaseModel, Field

class RAGCandidate(BaseModel):
    """
    Represents a similarity search result candidate from RAG system.

    Used to structure results from Elasticsearch vector similarity search
    before final classification decision.
    """

    main_category: str = Field(..., description="主案類名稱")
    sub_category: str = Field(..., description="子案類名稱")
    sub_description: str = Field(..., description="子案類描述")
    main_description: str = Field(..., description="主案類描述")
    keywords: List[str] = Field(default_factory=list, description="關鍵詞列表")
    similarity_score: float = Field(..., description="向量相似性分數", ge=0.0, le=1.0)

    class Config:
        """Pydantic configuration"""
        validate_assignment = True
        use_enum_values = True
```

### 5. ClassificationResult Entity

**File:** `src/domain/entities/classification_result.py`

```python
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, field_serializer
from .intent_type import IntentType
from .confidence_level import ConfidenceLevel
from .rag_candidate import RAGCandidate

class ClassificationResult(BaseModel):
    """
    Represents the complete classification result for a citizen complaint.

    Contains the final category assignment, detected intents, confidence
    assessment, and reasoning for the classification decision.
    """

    case_id: str = Field(..., description="原始投訴識別碼")

    # Primary classification results
    main_category: str = Field(..., description="分配的主案類")
    sub_category: str = Field(..., description="分配的子案類")
    sub_description: str = Field(..., description="子案類描述")

    # Multi-intent detection results
    intents: List[IntentType] = Field(
        default_factory=list,
        description="檢測到的意圖類型列表"
    )

    # Confidence and reasoning
    confidence: ConfidenceLevel = Field(..., description="分類信心等級")
    reasoning: str = Field(..., description="分類決策的說明理由")

    # Supporting data
    rag_candidates: List[RAGCandidate] = Field(
        default_factory=list,
        description="RAG相似性搜索候選結果"
    )

    similarity_score: float = Field(
        ...,
        description="最高相似性分數",
        ge=0.0,
        le=1.0
    )

    # Metadata
    processed_at: datetime = Field(
        default_factory=datetime.now,
        description="處理完成時間戳"
    )

    processing_time_ms: Optional[int] = Field(
        None,
        description="處理時間（毫秒）",
        ge=0
    )

    @field_serializer('processed_at')
    def serialize_processed_at(self, value: datetime) -> str:
        """將processed_at格式化為Elasticsearch所需的yyyy/MM/dd HH:mm:ss格式"""
        return value.strftime('%Y/%m/%d %H:%M:%S')

    @field_serializer('intents')
    def serialize_intents(self, value: List[IntentType]) -> List[str]:
        """將意圖枚舉轉換為字符串列表"""
        return [intent.value for intent in value]

    class Config:
        """Pydantic configuration"""
        validate_assignment = True
        use_enum_values = True
```

## Data Flow and Integration Patterns

### Input Processing Flow

```
1. Raw JSON Input → ComplaintInput Entity
   ↓ (Pydantic validation)
2. Validated ComplaintInput → Embedding Generation (GeminiEmbedder)
   ↓ (gemini-embedding-001, task_type="RETRIEVAL_QUERY")
3. Embedding → Similarity Search (ElasticsearchRepository)
   ↓ (cosine similarity against sub_categories index)
4. Search Results → RAGCandidate Entities
   ↓ (ranking and filtering)
5. RAGCandidates + Original Complaint → Classification Logic
   ↓ (LLM-based decision making)
6. Final Decision → ClassificationResult Entity
```

### JSON Schema Specifications

#### ComplaintInput JSON Schema

```json
{
	"case_id": "COMP-2025-001",
	"subject": "檢舉市中心攤販佔用人行道",
	"content": "我想檢舉市中心有攤販長期佔用人行道，造成行人通行困難，希望相關單位盡快處理...",
	"metadata": {
		"source": "web_form",
		"district": "中正區",
		"priority": "normal"
	},
	"record_time": "2025/08/12 14:30:00"
}
```

#### ClassificationResult JSON Schema

```json
{
	"case_id": "COMP-2025-001",
	"main_category": "路霸排除",
	"sub_category": "攤販違規佔用人行道、騎樓",
	"sub_description": "處理攤販、商家或個人違規佔用人行道、騎樓等公共通行空間的行為...",
	"intents": ["檢舉告發", "請求協助"],
	"confidence": "high",
	"reasoning": "投訴內容明確提及攤販佔用人行道問題，與「攤販違規佔用人行道、騎樓」子案類高度匹配...",
	"rag_candidates": [
		{
			"main_category": "路霸排除",
			"sub_category": "攤販違規佔用人行道、騎樓",
			"sub_description": "處理攤販、商家或個人違規佔用人行道、騎樓等公共通行空間的行為...",
			"main_description": "專職處理任何形式違規佔用公眾通行空間的行為...",
			"keywords": ["攤販", "人行道", "騎樓", "佔用"],
			"similarity_score": 0.89
		}
	],
	"similarity_score": 0.89,
	"processed_at": "2025/08/12 14:30:05",
	"processing_time_ms": 1250
}
```

## API Integration Specifications

### GeminiEmbedder Integration

```python
# Existing interface - no modifications needed
async def generate_embedding(self, text: str) -> List[float]:
    # Uses task_type="RETRIEVAL_QUERY" for complaint classification
    # Maintains existing 3072-dimensional vector output
```

### ElasticsearchRepository Integration

```python
# Extension pattern for similarity search
async def search_similar_categories(
    self,
    embedding: List[float],
    min_score: float = 0.6,
    max_results: int = 10
) -> List[RAGCandidate]:
    # Queries existing sub_categories index
    # Returns ranked similarity results as RAGCandidate entities
```

## Validation and Error Handling

### Input Validation Rules

-   **case_id**: Non-empty string, 1-100 characters
-   **subject**: Non-empty string, 1-200 characters
-   **content**: Minimum 10 characters, maximum 10,000 characters
-   **metadata**: Optional dictionary, any key-value pairs
-   **intents**: Must be from predefined IntentType enumeration
-   **similarity_score**: Float between 0.0 and 1.0
-   **confidence**: Must be valid ConfidenceLevel enum value

### Error Response Patterns

```python
# Validation Error Example
{
  "error_type": "ValidationError",
  "field": "content",
  "message": "投訴內容必須至少包含10個字符",
  "invalid_value": "太短"
}

# Intent Validation Error Example
{
  "error_type": "InvalidIntentError",
  "message": "無效的意圖類型",
  "invalid_intents": ["無效意圖"],
  "valid_intents": ["檢舉告發", "請求協助", "抱怨問題", ...]
}
```

## Performance Considerations

### Memory Optimization

-   **Lazy Loading**: RAGCandidate lists populated only when needed
-   **Efficient Serialization**: Custom serializers for datetime and enum fields
-   **Validation Caching**: Pydantic model validation results cached where possible

### Processing Time Targets

-   **Entity Creation**: <50ms per entity
-   **Validation**: <10ms per ComplaintInput
-   **Serialization**: <20ms per ClassificationResult
-   **Total Entity Overhead**: <100ms per complaint classification

## Configuration Extensions

### Settings Class Extensions

```python
# Additional configuration fields needed
class Settings(BaseSettings):
    # Existing fields...

    # Classification-specific settings
    classification_min_similarity: float = Field(0.6, env="CLASSIFICATION_MIN_SIMILARITY")
    classification_high_confidence_threshold: float = Field(0.75, env="CLASSIFICATION_HIGH_CONFIDENCE_THRESHOLD")
    classification_max_candidates: int = Field(10, env="CLASSIFICATION_MAX_CANDIDATES")
    classification_timeout_seconds: int = Field(30, env="CLASSIFICATION_TIMEOUT_SECONDS")

    # Intent detection settings
    intent_detection_enabled: bool = Field(True, env="INTENT_DETECTION_ENABLED")
    max_intents_per_complaint: int = Field(5, env="MAX_INTENTS_PER_COMPLAINT")
```

## Testing Strategy

### Unit Test Coverage

-   **Entity Validation**: Test all Pydantic field validations
-   **Enum Operations**: Test IntentType and ConfidenceLevel methods
-   **Serialization**: Test JSON conversion and datetime formatting
-   **Edge Cases**: Test boundary conditions and invalid inputs

### Integration Test Requirements

-   **ElasticsearchRepository**: Test RAGCandidate creation from search results
-   **GeminiEmbedder**: Test complaint embedding generation compatibility
-   **End-to-End**: Test complete complaint → classification result pipeline

### Test Data Requirements

```python
# Sample test complaint inputs
VALID_COMPLAINT_INPUTS = [
    {
        "case_id": "TEST-001",
        "subject": "檢舉攤販佔用人行道",
        "content": "我要檢舉市中心有攤販長期佔用人行道，造成行人通行困難，希望相關單位盡快處理。",
        "metadata": {"test": True}
    }
]

# Expected classification outputs
EXPECTED_CLASSIFICATIONS = [
    {
        "main_category": "路霸排除",
        "sub_category": "攤販違規佔用人行道、騎樓",
        "intents": ["檢舉告發", "請求協助"],
        "confidence": "high"
    }
]
```

## Future Extensibility

### Planned Extensions

-   **Batch Processing Support**: Collections of ComplaintInput entities
-   **Audit Trail**: Additional metadata for classification history
-   **Performance Metrics**: Built-in timing and accuracy tracking
-   **Multi-language Support**: Unicode handling improvements

### API Evolution Strategy

-   **Backward Compatibility**: All entity changes maintain existing field contracts
-   **Version Support**: Entity schema versioning for future changes
-   **Migration Support**: Automated conversion between entity versions

---

**Document Version**: 1.0
**Created**: 2025-08-12
**Status**: Implementation Ready
**Dependencies**: Existing domain entities (MainCategory, SubCategory)
**Next Phase**: Implementation via task-generator
