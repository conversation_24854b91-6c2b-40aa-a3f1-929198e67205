"""
Gemini SDK vs Agno Agent 性能比較測試

比較直接使用 Google Gemini SDK 和使用 Agno Agent 框架的結構化響應性能差異。
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from typing import List, Optional

import google.generativeai as genai
from agno.agent import Agent
from agno.models.google import Gemini
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# 載入環境變數
current_dir = Path(__file__).parent
project_root = current_dir.parent
env_file = project_root / ".env"
if env_file.exists():
    load_dotenv(env_file)


class SimpleResponse(BaseModel):
    """簡單的結構化響應模型用於測試"""
    
    answer: str = Field(..., description="回答的內容")
    confidence: float = Field(..., description="信心分數，0-1之間", ge=0.0, le=1.0)
    keywords: List[str] = Field(..., description="關鍵詞列表")
    category: str = Field(..., description="分類")


class GeminiPerformanceTest:
    """Gemini 性能測試類"""
    
    def __init__(self):
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not self.gemini_api_key:
            raise ValueError("GEMINI_API_KEY 環境變數未設定")
        
        self.test_prompt = """
        請回答以下問題：什麼是人工智慧？
        
        請提供：
        1. 簡潔的答案
        2. 你對這個答案的信心分數（0-1）
        3. 3-5個相關關鍵詞
        4. 將問題分類為：技術、科學、教育、其他
        """
    
    def test_direct_gemini_sdk(self) -> dict:
        """測試直接使用 Gemini SDK 的結構化響應"""
        
        # 配置 Gemini API
        genai.configure(api_key=self.gemini_api_key)
        
        # 定義響應 schema，對應 Pydantic 模型
        response_schema = {
            "type": "object",
            "properties": {
                "answer": {
                    "type": "string",
                    "description": "回答的內容"
                },
                "confidence": {
                    "type": "number",
                    "description": "信心分數，0-1之間"
                },
                "keywords": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "關鍵詞列表"
                },
                "category": {
                    "type": "string",
                    "description": "分類"
                }
            },
            "required": ["answer", "confidence", "keywords", "category"]
        }
        
        # 測量初始化時間
        init_start = time.time()
        model = genai.GenerativeModel("gemini-2.5-flash")
        init_time = time.time() - init_start
        
        # 測量請求響應時間
        request_start = time.time()
        
        response = model.generate_content(
            self.test_prompt,
            generation_config=genai.GenerationConfig(
                response_mime_type="application/json",
                response_schema=response_schema,
                temperature=0.1
            )
        )
        
        request_time = time.time() - request_start
        
        # 解析響應
        try:
            response_data = SimpleResponse.model_validate_json(response.text)
            parsing_success = True
            parsed_data = response_data.model_dump()
        except Exception as e:
            parsing_success = False
            parsed_data = {"error": str(e), "raw_response": response.text}
        
        return {
            "method": "Direct Gemini SDK",
            "init_time_ms": round(init_time * 1000, 2),
            "request_time_ms": round(request_time * 1000, 2),
            "total_time_ms": round((init_time + request_time) * 1000, 2),
            "parsing_success": parsing_success,
            "response_data": parsed_data
        }
    
    def test_agno_agent(self) -> dict:
        """測試使用 Agno Agent 的結構化響應"""
        
        # 測量初始化時間
        init_start = time.time()
        
        agent = Agent(
            model=Gemini(
                id="gemini-2.5-flash",
                api_key=self.gemini_api_key,
                temperature=0.1,
            ),
            response_model=SimpleResponse,
            structured_outputs=True,
            markdown=False,
            telemetry=False,
            monitoring=False,
        )
        
        init_time = time.time() - init_start
        
        # 測量請求響應時間
        request_start = time.time()
        
        response = agent.run(self.test_prompt)
        
        request_time = time.time() - request_start
        
        # 解析響應
        try:
            if hasattr(response.content, 'model_dump'):
                parsed_data = response.content.model_dump()
                parsing_success = True
            else:
                parsed_data = {"content": str(response.content)}
                parsing_success = True
        except Exception as e:
            parsing_success = False
            parsed_data = {"error": str(e), "raw_response": str(response.content)}
        
        return {
            "method": "Agno Agent",
            "init_time_ms": round(init_time * 1000, 2),
            "request_time_ms": round(request_time * 1000, 2),
            "total_time_ms": round((init_time + request_time) * 1000, 2),
            "parsing_success": parsing_success,
            "response_data": parsed_data
        }
    
    def run_comparison_test(self) -> dict:
        """執行完整的性能比較測試"""
        
        print("開始 Gemini SDK vs Agno Agent 性能比較測試...")
        print("=" * 60)
        
        # 測試直接 Gemini SDK
        print("測試直接 Gemini SDK...")
        sdk_result = self.test_direct_gemini_sdk()
        
        print(f"✓ 直接 SDK 完成 - 總時間: {sdk_result['total_time_ms']}ms")
        
        # 測試 Agno Agent
        print("測試 Agno Agent...")
        agno_result = self.test_agno_agent()
        
        print(f"✓ Agno Agent 完成 - 總時間: {agno_result['total_time_ms']}ms")
        
        # 計算性能差異
        time_difference = agno_result['total_time_ms'] - sdk_result['total_time_ms']
        performance_ratio = agno_result['total_time_ms'] / sdk_result['total_time_ms']
        
        comparison_result = {
            "sdk_result": sdk_result,
            "agno_result": agno_result,
            "performance_analysis": {
                "time_difference_ms": round(time_difference, 2),
                "agno_slower_by_factor": round(performance_ratio, 2),
                "agno_overhead_percentage": round((performance_ratio - 1) * 100, 2)
            }
        }
        
        return comparison_result
    
    def print_detailed_report(self, results: dict):
        """輸出詳細的性能報告"""
        
        print("\n" + "=" * 60)
        print("詳細性能報告")
        print("=" * 60)
        
        sdk = results["sdk_result"]
        agno = results["agno_result"]
        analysis = results["performance_analysis"]
        
        print(f"\n📊 直接 Gemini SDK:")
        print(f"   初始化時間: {sdk['init_time_ms']}ms")
        print(f"   請求時間: {sdk['request_time_ms']}ms")
        print(f"   總時間: {sdk['total_time_ms']}ms")
        print(f"   解析成功: {sdk['parsing_success']}")
        
        print(f"\n🤖 Agno Agent:")
        print(f"   初始化時間: {agno['init_time_ms']}ms")
        print(f"   請求時間: {agno['request_time_ms']}ms")  
        print(f"   總時間: {agno['total_time_ms']}ms")
        print(f"   解析成功: {agno['parsing_success']}")
        
        print(f"\n⚡ 性能比較:")
        print(f"   時間差異: {analysis['time_difference_ms']}ms")
        print(f"   Agno Agent 比 SDK 慢: {analysis['agno_slower_by_factor']}x")
        print(f"   Agno 額外開銷: {analysis['agno_overhead_percentage']}%")
        
        if analysis['agno_overhead_percentage'] > 50:
            print(f"\n⚠️  發現顯著性能差異!")
            print(f"   Agno Agent 比直接 SDK 慢了 {analysis['agno_overhead_percentage']}%")
            print(f"   建議檢查 Agno 框架配置或考慮直接使用 SDK")
        elif analysis['agno_overhead_percentage'] > 20:
            print(f"\n📝 適度性能差異")
            print(f"   Agno Agent 有 {analysis['agno_overhead_percentage']}% 的額外開銷")
            print(f"   這可能是框架抽象層的正常開銷")
        else:
            print(f"\n✅ 性能差異在合理範圍內")
        
        print(f"\n📋 響應內容對比:")
        if sdk['parsing_success'] and agno['parsing_success']:
            sdk_data = sdk['response_data']
            agno_data = agno['response_data']
            
            print(f"   SDK 回答: {sdk_data.get('answer', 'N/A')[:50]}...")
            print(f"   Agno 回答: {agno_data.get('answer', 'N/A')[:50]}...")
            print(f"   SDK 關鍵詞數量: {len(sdk_data.get('keywords', []))}")
            print(f"   Agno 關鍵詞數量: {len(agno_data.get('keywords', []))}")
        else:
            print(f"   解析失敗，無法比較內容")


def main():
    """主函數：執行性能測試"""
    
    try:
        tester = GeminiPerformanceTest()
        results = tester.run_comparison_test()
        tester.print_detailed_report(results)
        
        return results
        
    except Exception as e:
        print(f"❌ 測試執行失敗: {e}")
        raise


if __name__ == "__main__":
    main()