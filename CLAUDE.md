# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a well-architected AI RAG (Retrieval-Augmented Generation) application built with Python 3.12 following Clean Architecture principles. It processes Taiwan government complaint categories, generates embeddings using Google Gemini, and stores them in Elasticsearch 8.13.2 for vector-based retrieval.

## Development Commands

### Running the Application

```cmd
uv run src/main.py
```

### Installing Dependencies

```cmd
uv sync
```

### Adding New Dependencies

```cmd
uv add <package-name>
```

### Upgrading Dependencies

```cmd
uv lock --upgrade
```

### Environment Setup

Create a `.env` file with required environment variables:

```bash
# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Environment (local, dev, prod)
ENVIRONMENT=local
```

## Project Structure

### Core Files

-   **src/main.py**: Primary entry point for the application
-   **pyproject.toml**: Project configuration and dependencies (managed by uv)
-   **uv.lock**: Locked dependency versions for reproducible builds

### Clean Architecture Layers

-   **src/domain/**: Domain entities and interfaces (MainCategory, SubCategory)
-   **src/application/**: Use cases and business logic (ProcessCategoriesUseCase)
-   **src/infrastructure/**: External adapters (GeminiEmbedder, ElasticsearchRepository)
-   **src/config/**: Environment-based configuration management
-   **src/data/**: Taiwan government complaint category definitions

## Key Technologies

-   **Python 3.12**: Required Python version (specified in .python-version)
-   **uv**: Package and environment manager for Python
-   **Elasticsearch 8.13.2**: Vector database for RAG implementation with dense vector support
-   **Google Generative AI**: Gemini embedding-001 model for text embeddings
-   **Pydantic**: Data validation and settings management
-   **aiohttp**: Async HTTP client for API calls

## Architecture Notes

This RAG application implements Clean Architecture with the following layers:

### Domain Layer

-   **Entities**: MainCategory and SubCategory with Pydantic validation
-   **Interfaces**: Abstract base classes for embedders and repositories

### Application Layer

-   **Use Cases**: ProcessCategoriesUseCase orchestrates the workflow
-   **Business Logic**: Handles data transformation and embedding generation

### Infrastructure Layer

-   **GeminiEmbedder**: Async embedding generation with batch processing
-   **ElasticsearchRepository**: Vector storage and retrieval operations
-   **Configuration**: Environment-aware settings management

### Current Implementation Status

✅ **Completed Features:**

-   Complete data ingestion pipeline from JSON to Elasticsearch
-   Async embedding generation using Google Gemini
-   Vector index creation and management
-   Comprehensive error handling and logging
-   Environment-based configuration

❌ **Planned Features:**

-   Semantic search and retrieval endpoints
-   Query processing and response generation
-   REST API interface

### Data Processing Pipeline

1. Loads Taiwan government complaint categories from JSON
2. Extracts keywords and creates combined text representations
3. Generates embeddings using Gemini embedding-001
4. Indexes data in Elasticsearch with proper Chinese text analysis
