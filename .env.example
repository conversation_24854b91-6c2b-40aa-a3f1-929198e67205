# Google Gemini API
GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API (for intent detection fallback)
OPENAI_API_KEY=your_openai_api_key_here

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELAST<PERSON><PERSON>ARCH_PASSWORD=

# Environment (local, dev, prod)
ENVIRONMENT=local

# Intent Detection Configuration (optional overrides)
# INTENT_DETECTION_TEMPERATURE=0.1
# MAX_CONCURRENT_INTENTS=10
# ENABLE_CUSTOM_INTENTS=true