"""Intent detection configuration settings."""

from pydantic import Field

from .base_llm_config import BaseConfig
from .llm import APIProvider, LLMSettings


class IntentDetectionSettings(BaseConfig):
    """Configuration for intent detection and summarization engine."""

    # LLM Configuration
    llm_settings: LLMSettings = Field(default_factory=LLMSettings)
    
    # Primary and Fallback Providers
    primary_provider: APIProvider = Field(default=APIProvider.GOOGLE)
    fallback_provider: APIProvider = Field(default=APIProvider.OPENAI)
    
    # Model Configuration
    max_tokens: int = Field(default=4096, ge=100, le=4096)
    temperature: float = Field(default=0.1, ge=0.0, le=1.0)
    
    # Content Processing
    min_content_length: int = Field(default=10, ge=5, le=50)
    max_content_length: int = Field(default=10000, ge=100)
    
    # Performance Settings
    request_timeout: float = Field(default=30.0, ge=5.0, le=120.0)
    enable_fallback: bool = Field(default=True)
    
    def get_primary_model(self) -> str:
        """Get primary model for intent detection."""
        return self.llm_settings.get_default_model(self.primary_provider)
    
    def get_fallback_model(self) -> str:
        """Get fallback model for intent detection."""
        return self.llm_settings.get_default_model(self.fallback_provider)
    
    def get_primary_api_key(self) -> str | None:
        """Get primary API key for intent detection."""
        return self.llm_settings.get_api_key(self.primary_provider)
    
    def get_fallback_api_key(self) -> str | None:
        """Get fallback API key for intent detection."""
        return self.llm_settings.get_api_key(self.fallback_provider)
    
    def has_valid_primary_key(self) -> bool:
        """Check if primary API key is valid."""
        return self.llm_settings.is_provider_available(self.primary_provider)
    
    def has_valid_fallback_key(self) -> bool:
        """Check if fallback API key is valid."""
        return self.llm_settings.is_provider_available(self.fallback_provider)
    
    def get_generation_config(self) -> dict:
        """Get generation configuration for intent detection."""
        return {
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.request_timeout,
        }
