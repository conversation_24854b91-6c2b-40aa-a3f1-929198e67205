"""Custom exceptions for intent detection functionality."""


class IntentDetectionError(Exception):
    """Base exception for all intent detection errors."""

    pass


class InsufficientContentError(IntentDetectionError):
    """Raised when complaint content is too short for proper analysis."""

    pass


class ModelAnalysisError(IntentDetectionError):
    """Raised when LLM model analysis fails."""

    pass


class IntentValidationError(IntentDetectionError):
    """Raised when detected intent validation fails."""

    pass
