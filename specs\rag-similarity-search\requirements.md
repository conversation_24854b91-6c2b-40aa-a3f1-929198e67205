# Requirements: RAG Similarity Search

## 1. Feature Overview

The RAG Similarity Search feature provides vector-based semantic similarity retrieval for Taiwan government complaint categories. It extends the existing Elasticsearch infrastructure to support query-based similarity matching with configurable thresholds and comprehensive result ranking.

## 2. Functional Requirements (EARS Format)

### Core Similarity Search

**REQ-RSS-001: Embedding Query Generation**
- **Given** a complaint text input in Traditional Chinese
- **When** the system needs to find similar categories
- **Then** the system shall generate embeddings using the existing GeminiEmbedder with task_type="RETRIEVAL_QUERY"

**REQ-RSS-002: Vector Similarity Matching**
- **Given** a complaint embedding vector (3072 dimensions)
- **When** performing similarity search against existing category indices
- **Then** the system shall execute cosine similarity queries against both sub_categories and main_categories indices

**REQ-RSS-003: Threshold-Based Filtering**
- **Given** similarity search results with cosine similarity scores
- **When** applying relevance filtering
- **Then** the system shall filter results using a primary threshold of 0.75 and fallback threshold of 0.6

**REQ-RSS-004: Result Ranking and Limiting**
- **Given** filtered similarity search results
- **When** preparing candidate categories for classification
- **Then** the system shall rank results by similarity score descending and limit to top 5 candidates per index

### Edge Case Handling

**REQ-RSS-005: Low Similarity Fallback**
- **Given** no results exceed the primary threshold of 0.75
- **When** the system requires category candidates
- **Then** the system shall retry with fallback threshold of 0.6 and mark results as low confidence

**REQ-RSS-006: No Results Handling**
- **Given** no results exceed even the fallback threshold of 0.6
- **When** the system requires category candidates
- **Then** the system shall return an empty result set with appropriate status indicators

**REQ-RSS-007: Empty Query Handling**
- **Given** an empty or whitespace-only complaint text
- **When** attempting similarity search
- **Then** the system shall raise a validation error with descriptive message

### Performance Requirements

**REQ-RSS-008: Query Response Time**
- **Given** a single complaint embedding query
- **When** executing similarity search across category indices
- **Then** the system shall complete the search within 500ms (excluding embedding generation time)

**REQ-RSS-009: Concurrent Query Support**
- **Given** multiple simultaneous complaint queries
- **When** processing batch similarity searches
- **Then** the system shall support concurrent execution without performance degradation

### Integration Requirements

**REQ-RSS-010: GeminiEmbedder Reuse**
- **Given** the existing GeminiEmbedder infrastructure
- **When** implementing similarity search
- **Then** the system shall reuse the existing GeminiEmbedder without modification

**REQ-RSS-011: Elasticsearch Index Utilization**
- **Given** existing sub_categories and main_categories indices
- **When** performing similarity searches
- **Then** the system shall leverage existing indices without schema modifications

**REQ-RSS-012: Repository Extension**
- **Given** the existing ElasticsearchRepository pattern
- **When** implementing search functionality
- **Then** the system shall extend the repository following established patterns and interfaces

### Data Quality Requirements

**REQ-RSS-013: Result Structure Validation**
- **Given** similarity search results from Elasticsearch
- **When** returning candidate categories
- **Then** the system shall validate and structure results as RAGCandidate entities with category_id, similarity_score, and category_data

**REQ-RSS-014: Chinese Text Processing**
- **Given** Traditional Chinese complaint text
- **When** performing similarity matching
- **Then** the system shall utilize existing ik_max_word analyzer for optimal Chinese text segmentation

**REQ-RSS-015: Similarity Score Precision**
- **Given** cosine similarity calculations
- **When** returning similarity scores
- **Then** the system shall provide scores with minimum 4 decimal places precision for accurate threshold comparisons

### Error Handling Requirements

**REQ-RSS-016: Elasticsearch Connection Errors**
- **Given** Elasticsearch connectivity issues
- **When** attempting similarity search
- **Then** the system shall raise ElasticsearchConnectionError with retry suggestions

**REQ-RSS-017: Embedding Generation Failures**
- **Given** GeminiEmbedder API failures or timeouts
- **When** generating query embeddings
- **Then** the system shall propagate the embedding error with context about the similarity search operation

**REQ-RSS-018: Index Unavailability**
- **Given** missing or unavailable category indices
- **When** executing similarity queries
- **Then** the system shall raise specific errors indicating which indices are unavailable

## 3. Non-Functional Requirements

### Performance Constraints

- **Maximum Query Latency**: 500ms for similarity search operations (excluding embedding time)
- **Concurrent Query Capacity**: Support minimum 20 concurrent similarity searches
- **Memory Usage**: Maintain memory efficiency for embedding vector operations

### Scalability Requirements

- **Result Set Scalability**: Handle category indices with up to 10,000 categories efficiently
- **Query Volume**: Support batch processing of 100+ complaint queries with concurrent similarity searches

### Reliability Requirements

- **Error Recovery**: Graceful handling of partial search failures
- **Consistency**: Deterministic similarity scoring for identical query inputs
- **Availability**: 99% uptime for similarity search operations

## 4. Acceptance Criteria Summary

1. ✅ Reuses existing GeminiEmbedder without modification
2. ✅ Leverages existing Elasticsearch indices without schema changes
3. ✅ Implements configurable similarity thresholds (0.75 primary, 0.6 fallback)
4. ✅ Returns ranked similarity results as structured RAGCandidate entities
5. ✅ Handles edge cases (low similarity, no results, empty queries)
6. ✅ Supports concurrent processing for batch operations
7. ✅ Completes search operations within 500ms performance target
8. ✅ Follows established Clean Architecture patterns
9. ✅ Provides comprehensive error handling with specific error types
10. ✅ Maintains 4+ decimal precision for similarity scores

## 5. Dependencies

### Internal Dependencies
- **GeminiEmbedder**: For query embedding generation
- **ElasticsearchRepository**: Base repository pattern for extension
- **RAGCandidate**: Domain entity for structured result representation
- **Settings**: Configuration management for thresholds and connection parameters

### External Dependencies
- **Elasticsearch 8.13.2**: Vector search infrastructure with cosine similarity support
- **Google Generative AI**: Gemini embedding-001 model via existing integration

## 6. Success Metrics

- **Accuracy**: Similarity search returns relevant categories in top 5 results >85% of the time
- **Performance**: 95% of queries complete within 500ms target
- **Reliability**: <2% error rate across all similarity search operations
- **Coverage**: Successfully handles 100% of valid complaint text inputs without system failures