"""Unit tests for DetectIntentsUseCase."""

from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from src.application.use_cases.detect_intents import (
    DetectIntentsUseCase,
    IntentDetectionMetrics,
)
from src.domain.entities.detected_intent import DetectedIntent
from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.intent_detection_result import IntentDetectionResult
from src.domain.entities.intent_type import IntentType
from src.infrastructure.intent_analysis.agno_intent_analyzer import (
    IntentDetectionConfig,
)


class TestDetectIntentsUseCase:
    """Test DetectIntentsUseCase functionality."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return IntentDetectionConfig(
            primary_api_key="test-gemini-key",
            fallback_api_key="test-openai-key",
            min_content_length=10,
        )

    @pytest.fixture
    def mock_analyzer(self):
        """Create mock intent analyzer."""
        mock = Mock()
        mock.analyze_intents = AsyncMock()
        return mock

    @pytest.fixture
    def use_case(self, config, mock_analyzer):
        """Create use case with mocked analyzer."""
        use_case = DetectIntentsUseCase(config)
        use_case.intent_analyzer = mock_analyzer
        return use_case

    @pytest.mark.asyncio
    async def test_successful_intent_detection(self, use_case, mock_analyzer):
        """Test successful intent detection and summarization."""
        # Setup
        request = IntentDetectionRequest(
            complaint_id="test-001",
            complaint_subject="服務態度問題",
            complaint_content="我對於區公所服務人員的態度非常不滿，希望能夠改善相關服務品質。",
        )

        expected_intents = [
            DetectedIntent(
                intent_name=IntentType.DISSATISFIED_SERVICE_ATTITUDE.value,
                is_predefined=True,
                reasoning="文中明確表達對服務人員態度的不滿",
                text_evidence=["服務人員的態度非常不滿"],
            ),
            DetectedIntent(
                intent_name=IntentType.REQUEST_IMPROVEMENT.value,
                is_predefined=True,
                reasoning="請求改善服務品質",
                text_evidence=["希望能夠改善相關服務品質"],
            ),
        ]

        expected_result = IntentDetectionResult(
            complaint_id="test-001",
            detected_intents=expected_intents,
            content_summary="民眾對區公所服務人員態度不滿，要求改善服務品質",
            processing_time_ms=1200,
        )

        mock_analyzer.analyze_intents.return_value = expected_result

        # Execute
        result = await use_case.execute(request)

        # Assert
        assert result.complaint_id == "test-001"
        assert len(result.detected_intents) == 2
        assert (
            result.detected_intents[0].intent_name
            == IntentType.DISSATISFIED_SERVICE_ATTITUDE.value
        )
        assert (
            result.detected_intents[1].intent_name
            == IntentType.REQUEST_IMPROVEMENT.value
        )
        assert (
            result.content_summary == "民眾對區公所服務人員態度不滿，要求改善服務品質"
        )
        assert result.error_message is None

        # Verify analyzer was called with correct request
        mock_analyzer.analyze_intents.assert_called_once_with(request)

    @pytest.mark.asyncio
    async def test_insufficient_content_error(self, use_case):
        """Test handling of insufficient content error."""
        request = IntentDetectionRequest(
            complaint_id="test-002",
            complaint_subject="短內容",
            complaint_content="太短",  # Only 2 characters, below min_content_length
        )

        result = await use_case.execute(request)

        assert result.complaint_id == "test-002"
        assert len(result.detected_intents) == 0
        assert "內容過短" in result.error_message
        assert result.content_summary == "太短"

    @pytest.mark.asyncio
    async def test_invalid_language_error(self, use_case):
        """Test handling of invalid language error."""
        request = IntentDetectionRequest(
            complaint_id="test-003",
            complaint_subject="English Content",
            complaint_content="This is English content that should be rejected.",
        )

        result = await use_case.execute(request)

        assert result.complaint_id == "test-003"
        assert len(result.detected_intents) == 0
        assert "語言不符" in result.error_message
        assert result.content_summary == ""

    @pytest.mark.asyncio
    async def test_analyzer_failure_handling(self, use_case, mock_analyzer):
        """Test handling when analyzer fails."""
        request = IntentDetectionRequest(
            complaint_id="test-004",
            complaint_subject="測試",
            complaint_content="這是正常的繁體中文陳情內容，應該可以處理。",
        )

        # Mock analyzer to raise exception
        mock_analyzer.analyze_intents.side_effect = Exception("API連線失敗")

        result = await use_case.execute(request)

        assert result.complaint_id == "test-004"
        assert len(result.detected_intents) == 0
        assert "意圖檢測失敗" in result.error_message
        assert "API連線失敗" in result.error_message

    @pytest.mark.asyncio
    async def test_empty_content_validation(self, use_case):
        """Test validation of empty content."""
        request = IntentDetectionRequest(
            complaint_id="test-005",
            complaint_subject="空內容",
            complaint_content="   ",  # Only whitespace
        )

        result = await use_case.execute(request)

        assert result.complaint_id == "test-005"
        assert (
            "內容不能為空" in result.error_message or "內容過短" in result.error_message
        )

    def test_traditional_chinese_detection(self, use_case):
        """Test Traditional Chinese content detection."""
        # Valid Traditional Chinese
        valid_content = "這是繁體中文的陳情內容，包含足夠的中文字符。"
        assert use_case._is_traditional_chinese(valid_content) is True

        # English content
        english_content = "This is English content without Chinese characters."
        assert use_case._is_traditional_chinese(english_content) is False

        # Mixed content with sufficient Chinese
        mixed_content = "這是mixed content包含一些English words但主要是中文。"
        assert use_case._is_traditional_chinese(mixed_content) is True

        # Insufficient Chinese content
        minimal_chinese = "Hello 世界"  # Only 2 Chinese chars out of 8
        assert use_case._is_traditional_chinese(minimal_chinese) is False

    @pytest.mark.asyncio
    async def test_multi_intent_detection(self, use_case, mock_analyzer):
        """Test detection of multiple intents in a single complaint."""
        request = IntentDetectionRequest(
            complaint_id="test-multi",
            complaint_subject="複合問題",
            complaint_content="我要檢舉某單位違法行為，同時請求協助解決相關問題，並對服務態度表達不滿。",
        )

        expected_intents = [
            DetectedIntent(
                IntentType.REPORT_VIOLATION.value,
                True,
                "檢舉違法行為",
                ["檢舉某單位違法行為"],
            ),
            DetectedIntent(
                IntentType.REQUEST_ASSISTANCE.value,
                True,
                "請求協助",
                ["請求協助解決相關問題"],
            ),
            DetectedIntent(
                IntentType.DISSATISFIED_SERVICE_ATTITUDE.value,
                True,
                "服務態度不滿",
                ["服務態度表達不滿"],
            ),
        ]

        expected_result = IntentDetectionResult(
            complaint_id="test-multi",
            detected_intents=expected_intents,
            content_summary="民眾檢舉違法行為，請求協助並表達對服務態度不滿",
            processing_time_ms=1500,
        )

        mock_analyzer.analyze_intents.return_value = expected_result

        result = await use_case.execute(request)

        assert len(result.detected_intents) == 3
        assert all(intent.is_predefined for intent in result.detected_intents)
        intent_names = [intent.intent_name for intent in result.detected_intents]
        assert IntentType.REPORT_VIOLATION.value in intent_names
        assert IntentType.REQUEST_ASSISTANCE.value in intent_names
        assert IntentType.DISSATISFIED_SERVICE_ATTITUDE.value in intent_names


class TestIntentDetectionMetrics:
    """Test IntentDetectionMetrics functionality."""

    @pytest.fixture
    def metrics(self):
        """Create metrics instance."""
        return IntentDetectionMetrics()

    def test_record_successful_analysis(self, metrics):
        """Test recording successful analysis metrics."""
        result = IntentDetectionResult(
            complaint_id="test-metrics",
            detected_intents=[
                DetectedIntent("請求協助", True, "需要幫助", ["請協助"]),
                DetectedIntent("抱怨問題", True, "服務問題", ["不滿意"]),
            ],
            content_summary="民眾請求協助並抱怨服務問題",
            processing_time_ms=1000,
        )

        metrics.record_successful_analysis(result)

        assert len(metrics.processing_times) == 1
        assert metrics.processing_times[0] == 1000
        assert metrics.intent_frequencies["請求協助"] == 1
        assert metrics.intent_frequencies["抱怨問題"] == 1
        assert len(metrics.summary_lengths) == 1
        assert metrics.dual_task_success_rate[0] == 1  # Success

    def test_record_error(self, metrics):
        """Test recording error metrics."""
        metrics.record_error("InsufficientContentError")

        assert metrics.error_counts["InsufficientContentError"] == 1

    def test_performance_summary_empty(self, metrics):
        """Test performance summary with no data."""
        summary = metrics.get_performance_summary()
        assert summary["status"] == "no_data"

    def test_performance_summary_with_data(self, metrics):
        """Test performance summary with recorded data."""
        # Record some test data
        for i in range(5):
            result = IntentDetectionResult(
                complaint_id=f"test-{i}",
                detected_intents=[DetectedIntent("測試意圖", True, "測試", ["測試"])],
                content_summary="測試摘要" * 10,  # Different lengths
                processing_time_ms=1000 + i * 100,
            )
            metrics.record_successful_analysis(result)

        metrics.record_error("TestError")

        summary = metrics.get_performance_summary()

        assert "avg_processing_time_ms" in summary
        assert summary["total_analyses"] == 5
        assert "TestError" in summary["error_distribution"]
        assert summary["dual_task_success_rate"] == 1.0  # All successful
