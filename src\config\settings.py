"""Main application settings - simplified aggregator for all configurations."""

from pathlib import Path
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from .classification import ClassificationSettings
from .elasticsearch import ElasticsearchSettings
from .intent_detection import IntentDetectionSettings
from .llm import APIProvider, LLMSettings
from .rag import RAGSettings


class Settings(BaseSettings):
    """應用程式設定 - 重構後的統一配置管理"""

    # 專案根目錄（基於當前工作目錄）
    project_root: Path = Field(default_factory=lambda: Path.cwd())

    # 環境設定
    environment: str = Field(default="local", env="ENVIRONMENT")

    # 環境變數 API 密鑰（僅用於從環境讀取）
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")

    # 各個服務的獨立配置
    elasticsearch: ElasticsearchSettings = Field(default_factory=ElasticsearchSettings)
    llm: LLMSettings = Field(default_factory=LLMSettings)
    rag: RAGSettings = Field(default_factory=RAGSettings)
    intent_detection: IntentDetectionSettings = Field(
        default_factory=IntentDetectionSettings
    )
    classification: ClassificationSettings = Field(
        default_factory=ClassificationSettings
    )

    def model_post_init(self, __context=None) -> None:
        """Post-initialization setup."""
        self._initialize_api_keys()

    def _initialize_api_keys(self) -> None:
        """Initialize API keys in all LLM configurations."""
        # Set API keys in main LLM settings
        self.llm.set_api_key(APIProvider.GOOGLE, self.gemini_api_key)
        self.llm.set_api_key(APIProvider.OPENAI, self.openai_api_key)
        self.llm.set_api_key(APIProvider.ANTHROPIC, self.anthropic_api_key)

        # Set API keys in service-specific LLM settings
        self.rag.llm_settings.set_api_key(APIProvider.GOOGLE, self.gemini_api_key)
        self.rag.llm_settings.set_api_key(APIProvider.OPENAI, self.openai_api_key)
        self.rag.llm_settings.set_api_key(APIProvider.ANTHROPIC, self.anthropic_api_key)

        self.intent_detection.llm_settings.set_api_key(
            APIProvider.GOOGLE, self.gemini_api_key
        )
        self.intent_detection.llm_settings.set_api_key(
            APIProvider.OPENAI, self.openai_api_key
        )
        self.intent_detection.llm_settings.set_api_key(
            APIProvider.ANTHROPIC, self.anthropic_api_key
        )

        self.classification.llm_settings.set_api_key(
            APIProvider.GOOGLE, self.gemini_api_key
        )
        self.classification.llm_settings.set_api_key(
            APIProvider.OPENAI, self.openai_api_key
        )
        self.classification.llm_settings.set_api_key(
            APIProvider.ANTHROPIC, self.anthropic_api_key
        )

    model_config = SettingsConfigDict(
        extra="ignore",  # Ignore extra fields for backward compatibility
        case_sensitive=False,
        env_file=".env",
        env_file_encoding="utf-8",
    )
