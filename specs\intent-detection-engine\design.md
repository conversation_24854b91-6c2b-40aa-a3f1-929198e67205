# Technical Design: Intent Detection Engine

## Overview

This document provides the complete technical design for the intent detection engine, a critical component of the Complaint Classification Agent that analyzes citizen complaints to identify multiple intents and generate content summaries using both predefined Taiwan government categories and flexible custom intent identification. The engine uses the Agno framework with **Pydantic response_model for structured output**, ensuring 100% format compliance and eliminating manual JSON parsing errors. This dual-task approach improves vector similarity matching by filtering noise and extracting key points from complaint content.

## Architecture Overview

### System Context

```
┌─────────────────────────────────────────────────────────┐
│                Classification Orchestrator              │
│                                                         │
│  ┌─────────────────┐     ┌────────────────────────────┐ │
│  │   Complaint     │────▶│    Intent Detection        │ │
│  │   Input Data    │     │    + Summarization         │ │
│  └─────────────────┘     │       Engine               │ │
│                          └────────────────────────────┘ │
│                                     │                   │
│                          ┌──────────▼──────────┐        │
│                          │ Intent Results +    │        │
│                          │ Content Summary     │        │
│                          └─────────────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### Component Architecture

The intent detection engine follows Clean Architecture principles and leverages the Agno framework for flexible LLM provider integration:

```
┌───────────────────────────────────────────────────────────┐
│                    Application Layer                      │
│  ┌─────────────────────────────────────────────────────┐  │
│  │              DetectIntentsUseCase                   │  │
│  │                                                     │  │
│  │  • Orchestrates intent detection + summarization   │  │
│  │  • Uses IntentType enum for intent validation      │  │
│  │  • Handles multi-intent coordination               │  │
│  │  • Coordinates dual-task processing workflow       │  │
│  │  • Integrates with Agno Agent framework            │  │
│  └─────────────────────────────────────────────────────┘  │
└───────────────────────────────────────────────────────────┘
                              │
┌───────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                    │
│                                                           │
│  ┌─────────────────────────────────────────────────────┐  │
│  │           AgnoIntentAnalyzer                        │  │
│  │                                                     │  │
│  │  • Dual-task LLM analysis using Agno framework     │  │
│  │  • Support for multiple model providers:           │  │
│  │    - Gemini 2.5 Flash (primary)                    │  │
│  │    - GPT-4.1 Mini (alternative)                    │  │
│  │  • Prompt engineering for Traditional Chinese      │  │
│  │  • Intent validation using IntentType enum         │  │
│  │  • Content summarization with noise filtering     │  │
│  │  • Conflict resolution for competing intents       │  │
│  └─────────────────────────────────────────────────────┘  │
│                              │                             │
│  ┌─────────────────────────────────────────────────────┐  │
│  │              Agno Agent Framework                   │  │
│  │                                                     │  │
│  │  • Unified interface to 23+ model providers        │  │
│  │  • Performance optimized (3μs instantiation)       │  │
│  │  • Native multi-modal support                      │  │
│  │  • Built-in reasoning and tool capabilities        │  │
│  └─────────────────────────────────────────────────────┘  │
└───────────────────────────────────────────────────────────┘
```

### Domain Layer Extensions

New domain entities will be added to support intent detection and content summarization (simplified without confidence fields):

```python
# Domain Entities (extends existing complaint-classification-entities)

@dataclass
class IntentDetectionRequest:
    complaint_id: str
    complaint_subject: str
    complaint_content: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class DetectedIntent:
    intent_name: str
    is_predefined: bool  # True for Taiwan govt predefined intents
    reasoning: str
    text_evidence: List[str]  # Specific text snippets supporting this intent

@dataclass
class IntentDetectionResult:
    complaint_id: str
    detected_intents: List[DetectedIntent]
    content_summary: str  # Generated summary for vector embedding
    processing_time_ms: int
    error_message: Optional[str] = None
```

## Agno Framework Integration

### Multi-Model Agent Configuration

The intent detection engine uses Agno's unified model provider interface to support multiple LLM options:

```python
from agno.agent import Agent
from agno.models.gemini import Gemini
from agno.models.openai import OpenAIChat
from agno.tools.reasoning import ReasoningTools
from pydantic import BaseModel, Field
from typing import List, Optional
from src.domain.entities.intent_type import IntentType

# Pydantic Response Models for Structured Output
class DetectedIntentModel(BaseModel):
    """Pydantic model for individual detected intent with validation."""
    intent_name: str = Field(..., description="意圖名稱")
    is_predefined: bool = Field(..., description="是否為預設類別")
    reasoning: str = Field(..., description="判斷理由")
    text_evidence: List[str] = Field(..., description="支持文字證據陣列")

class IntentAnalysisResponse(BaseModel):
    """Main response model for intent analysis ensuring 100% format compliance."""
    detected_intents: List[DetectedIntentModel] = Field(..., description="識別出的意圖陣列")
    content_summary: str = Field(..., description="內容重點摘要（100-300字）")

class AgnoIntentAnalyzer:
    """
    Analyzes complaint text to detect multiple intents and generate summaries using Agno Agent framework.
    Supports multiple model providers with fallback capabilities.
    Performs dual tasks: intent detection and content summarization.
    """

    def __init__(self, config: IntentDetectionConfig):
        self.config = config
        # No need to load predefined intents - using enum class definitions directly

        # Primary agent with Gemini 2.5 Flash
        self.primary_agent = Agent(
            model=Gemini(
                id="gemini-2.5-flash",
                temperature=config.temperature,
                max_tokens=config.max_tokens
            ),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=self._get_agent_instructions(),
            response_model=IntentAnalysisResponse,  # Use Pydantic model for structured output
            structured_outputs=True
        )

        # Fallback agent with GPT-4.1 Mini
        self.fallback_agent = Agent(
            model=OpenAIChat(
                id="gpt-4.1-mini",
                temperature=config.temperature,
                max_tokens=config.max_tokens
            ),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=self._get_agent_instructions(),
            response_model=IntentAnalysisResponse,  # Use Pydantic model for structured output
            structured_outputs=True
        )

    async def analyze_intents(
        self,
        request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """
        Main entry point for dual-task analysis (intent detection + summarization) using Agno agents.
        """

        try:
            # Primary analysis with Gemini 2.5 Flash
            result = await self._analyze_with_agent(
                self.primary_agent,
                request
            )
            return result

        except Exception as primary_error:
            # Fallback to GPT-4.1 Mini
            try:
                result = await self._analyze_with_agent(
                    self.fallback_agent,
                    request
                )
                return result

            except Exception as fallback_error:
                # Return error result if both models fail
                return self._create_error_result(
                    request,
                    primary_error,
                    fallback_error
                )
```

### Agent Instructions and Prompt Engineering

```python
from src.domain.entities.intent_type import IntentType

def _get_agent_instructions(self) -> str:
    """
    Comprehensive instructions for the Agno agent to perform dual tasks: intent detection and content summarization.
    Optimized for Traditional Chinese complaint analysis with noise filtering.
    Uses IntentType enum to dynamically generate predefined intent categories.
    """

    # Generate predefined intent list dynamically from IntentType enum
    predefined_intents = IntentType.get_all_intents()

    # Create formatted intent list with descriptions
    intent_descriptions = {
        IntentType.REPORT_VIOLATION.value: "舉報違法或不當行為",
        IntentType.REQUEST_ASSISTANCE.value: "尋求政府單位協助解決問題",
        IntentType.COMPLAINT_PROBLEM.value: "對政府服務或政策表達不滿",
        IntentType.REQUEST_IMPROVEMENT.value: "建議改善現有服務或制度",
        IntentType.CONSULTATION_INQUIRY.value: "詢問相關法規或程序",
        IntentType.DISSATISFIED_SERVICE_ATTITUDE.value: "對承辦人員態度不滿",
        IntentType.DISSATISFIED_STAFF_PROFESSIONALISM.value: "對承辦人員專業能力不滿",
        IntentType.GRATITUDE_PRAISE.value: "表達感謝或肯定",
        IntentType.SUGGEST_PROPOSAL.value: "提供建設性意見",
        IntentType.OTHER.value: "其他意圖類別"
    }

    # Build intent list string dynamically
    intent_list = "\n".join([
        f"    {i+1}. {intent} - {intent_descriptions.get(intent, '相關意圖')}"
        for i, intent in enumerate(predefined_intents)
    ])

    return f"""
    你是專門分析台灣政府民眾陳情的AI助理。你的任務是執行兩項核心工作：
    1. 識別陳情內容中的所有相關意圖類別
    2. 生成簡潔的重點摘要，過濾雜訊內容

    ## 預設意圖類別（優先使用）：
{intent_list}

    ## 意圖分析原則：
    - 可同時識別多個意圖，不限制數量
    - 優先使用預設類別，若預設類別無法準確描述意圖，可提供更適合的自定義意圖名稱
    - 識別所有相關意圖，不考慮意圖間的衝突
    - 為每個識別出的意圖提供清楚的推理說明
    - 列出支持該意圖的具體文字證據

    ## 內容摘要原則：
    - 摘要長度控制在100-300個繁體中文字
    - 過濾掉無關的個人資訊、重複表達、情緒性詞彙等雜訊
    - 保留核心問題、具體事實、訴求重點
    - 使用適合政府行政處理的正式用語
    - 確保摘要內容與意圖分析一致

    ## 輸出要求：
    - 識別出的意圖列表，每個意圖包含名稱、推理說明和文字證據
    - 生成100-300字的內容重點摘要
    - 所有輸出將自動格式化為結構化數據，無需手動JSON格式化
    """
```

### Structured Output Processing with Pydantic Models

**Key Benefits of Using response_model:**

-   **100% Format Compliance**: Pydantic models ensure agent responses always match expected structure
-   **Type Safety**: Compile-time validation prevents runtime parsing errors
-   **Automatic Parsing**: No manual JSON extraction or parsing required
-   **Validation**: Built-in field validation and type checking
-   **Maintainability**: Clear contracts between agent and application code

```python
async def _analyze_with_agent(
    self,
    agent: Agent,
    request: IntentDetectionRequest
) -> IntentDetectionResult:
    """
    Performs intent analysis using the specified Agno agent.
    """

    # Construct dual-task analysis prompt
    analysis_prompt = f"""
    請分析以下台灣政府民眾陳情，執行意圖識別和內容摘要：

    陳情主旨：{request.complaint_subject}
    陳情內容：{request.complaint_content}

    請按照系統指示進行雙重分析：
    1. 識別所有相關意圖類別
    2. 生成簡潔的重點摘要（過濾雜訊）

    回應將自動結構化處理。
    """

    start_time = time.time()

    # Run agent analysis
    response = agent.run(analysis_prompt)

    processing_time = int((time.time() - start_time) * 1000)

    # Use structured response directly from Agno Agent (no manual parsing needed)
    # response is automatically parsed into IntentAnalysisResponse Pydantic model
    analysis_response: IntentAnalysisResponse = response

    # Transform Pydantic models to domain objects
    detected_intents = self._transform_to_domain_intents(analysis_response.detected_intents)

    return IntentDetectionResult(
        complaint_id=request.complaint_id,
        detected_intents=detected_intents,
        content_summary=analysis_response.content_summary,
        processing_time_ms=processing_time,
        error_message=None
    )

def _transform_to_domain_intents(
    self,
    intent_models: List[DetectedIntentModel]
) -> List[DetectedIntent]:
    """
    Transforms Pydantic intent models into domain objects.
    Uses IntentType enum to verify if intents are predefined.
    """

    detected_intents = []

    for intent_model in intent_models:
        # Use IntentType enum to determine if intent is predefined
        is_predefined = IntentType.is_valid_intent(intent_model.intent_name)

        detected_intent = DetectedIntent(
            intent_name=intent_model.intent_name,
            is_predefined=is_predefined,
            reasoning=intent_model.reasoning,
            text_evidence=intent_model.text_evidence
        )
        detected_intents.append(detected_intent)

    return detected_intents
```

## Performance Optimization

### Concurrent Processing Design

```python
class ConcurrentIntentProcessor:
    """
    Handles concurrent intent detection for batch processing using Agno agents.
    """

    def __init__(self, config: IntentDetectionConfig):
        self.config = config
        self.semaphore = asyncio.Semaphore(config.max_concurrent_requests)

        # Create agent pool for concurrent processing
        self.agent_pool = self._create_agent_pool()

    def _create_agent_pool(self) -> List[Agent]:
        """
        Creates a pool of Agno agents for concurrent processing.
        """

        agents = []
        pool_size = self.config.max_concurrent_requests

        for i in range(pool_size):
            # Alternate between primary and fallback models for load distribution
            if i % 2 == 0:
                model = Gemini(id="gemini-2.5-flash", temperature=self.config.temperature)
            else:
                model = OpenAIChat(id="gpt-4.1-mini", temperature=self.config.temperature)

            agent = Agent(
                model=model,
                tools=[ReasoningTools(add_instructions=True)],
                instructions=self._get_agent_instructions(),
                response_model=IntentAnalysisResponse,  # Use Pydantic model for structured output
                structured_outputs=True
            )
            agents.append(agent)

        return agents

    async def process_batch(
        self,
        requests: List[IntentDetectionRequest]
    ) -> List[IntentDetectionResult]:
        """
        Process multiple intent detection requests concurrently
        using agent pool with rate limiting.
        """

        async def process_single(request: IntentDetectionRequest, agent_index: int):
            async with self.semaphore:
                # Get agent from pool
                agent = self.agent_pool[agent_index % len(self.agent_pool)]

                # Perform analysis
                analyzer = AgnoIntentAnalyzer(self.config)
                analyzer.primary_agent = agent

                return await analyzer.analyze_intents(request)

        # Execute concurrent processing with agent pool
        tasks = [
            process_single(req, idx)
            for idx, req in enumerate(requests)
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions and return structured results
        return self._process_batch_results(results, requests)
```

## Error Handling and Fallback

### Comprehensive Error Handling

```python
class IntentDetectionErrorHandler:
    """
    Handles various error scenarios with appropriate fallback strategies.
    Simplified without confidence-based error responses.
    """

    async def handle_analysis_error(
        self,
        request: IntentDetectionRequest,
        primary_error: Exception,
        fallback_error: Optional[Exception] = None
    ) -> IntentDetectionResult:
        """
        Implements fallback strategies for different error types.
        """

        if isinstance(primary_error, InsufficientContentError):
            # Attempt keyword-based analysis for very short complaints
            return await self._analyze_short_content(request)

        elif fallback_error is None:
            # Only primary model failed - return generic error
            return self._create_error_result(request, primary_error)

        else:
            # Both models failed - return comprehensive error
            return IntentDetectionResult(
                complaint_id=request.complaint_id,
                detected_intents=[],
                content_summary="",
                processing_time_ms=0,
                error_message=f"意圖檢測失敗，主要模型錯誤: {str(primary_error)[:100]}, 備用模型錯誤: {str(fallback_error)[:100]}"
            )

    async def _analyze_short_content(
        self,
        request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """
        Simplified intent analysis for very short complaints using original content directly.
        """

        # Try to analyze short content directly with LLM using original text
        try:
            # Use original content for intent detection
            result = await self._analyze_with_agent(
                self.primary_agent,
                request
            )
            return result

        except Exception:
            # If LLM fails, return minimal result using original content
            return IntentDetectionResult(
                complaint_id=request.complaint_id,
                detected_intents=[],
                content_summary=request.complaint_content if len(request.complaint_content) > 10 else "內容過短無法摘要",
                processing_time_ms=50,
                error_message="內容過短，無法識別意圖"
            )
```

## API Specifications

### Use Case Interface

```python
class DetectIntentsUseCase:
    """
    Application layer use case for intent detection and content summarization using Agno framework.
    Performs dual tasks to support enhanced vector similarity matching.
    """

    def __init__(
        self,
        config: IntentDetectionConfig,
        error_handler: IntentDetectionErrorHandler
    ):
        self.config = config
        self.intent_analyzer = AgnoIntentAnalyzer(config)
        self.error_handler = error_handler
        self.metrics = IntentDetectionMetrics()

    async def execute(
        self,
        request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """
        Main execution method for dual-task processing: intent detection + content summarization.

        Args:
            request: Validated intent detection request

        Returns:
            IntentDetectionResult with all detected intents and content summary

        Raises:
            IntentDetectionError: For unrecoverable errors
        """

        try:
            # Input validation
            self._validate_request(request)

            # Perform intent analysis
            result = await self._perform_intent_analysis(request)

            # Record metrics
            self.metrics.record_successful_analysis(result)

            return result

        except Exception as e:
            # Handle errors with fallback strategies
            self.metrics.record_error(type(e).__name__)
            return await self.error_handler.handle_analysis_error(request, e)

    async def _perform_intent_analysis(
        self,
        request: IntentDetectionRequest
    ) -> IntentDetectionResult:
        """Core dual-task analysis logic using Agno framework."""
        return await self.intent_analyzer.analyze_intents(request)
```

### Integration Points

```python
# Integration with Classification Orchestrator

class ClassificationOrchestrator:
    """
    Updated to include intent detection and summarization in classification workflow using Agno.
    The generated summary and intents are used for enhanced vector similarity matching.
    """

    def __init__(
        self,
        # ... existing dependencies
        detect_intents_use_case: DetectIntentsUseCase
    ):
        # ... existing initialization
        self.detect_intents_use_case = detect_intents_use_case

    async def classify_complaint(
        self,
        complaint_input: ComplaintInput
    ) -> ClassificationResult:
        """
        Enhanced classification workflow with intent detection and content summarization.
        Uses the generated summary and intents for improved vector similarity matching.
        """

        # Step 1: Intent detection + content summarization using Agno framework
        intent_request = IntentDetectionRequest(
            complaint_id=complaint_input.id,
            complaint_subject=complaint_input.subject,
            complaint_content=complaint_input.content
        )

        intent_results = await self.detect_intents_use_case.execute(intent_request)

        # Step 2: Enhanced RAG similarity search using summary + intents
        # Combine content summary with detected intents for embedding
        enhanced_query = self._create_enhanced_query(
            intent_results.content_summary,
            intent_results.detected_intents
        )

        similarity_results = await self.rag_search_use_case.execute_with_enhanced_query(
            complaint_input,
            enhanced_query
        )

        # Step 3: Final classification with combined context
        return await self._perform_final_classification(
            complaint_input,
            similarity_results,
            intent_results
        )

    def _create_enhanced_query(
        self,
        content_summary: str,
        detected_intents: List[DetectedIntent]
    ) -> str:
        """
        Creates an enhanced query combining summary and intent information
        for better vector similarity matching.
        """

        # Extract intent names for query enhancement
        intent_names = [intent.intent_name for intent in detected_intents]
        intent_text = "、".join(intent_names)

        # Combine summary with intent information
        enhanced_query = f"摘要：{content_summary} 意圖：{intent_text}"

        return enhanced_query
```

## Configuration

### Intent Detection Configuration

```python
@dataclass
class IntentDetectionConfig:
    """Configuration for intent detection and summarization engine using Agno framework."""

    # Primary Model Configuration (Gemini 2.5 Flash)
    primary_model: str = "gemini-2.5-flash"
    primary_api_key: Optional[str] = None

    # Fallback Model Configuration (GPT-4.1 Mini)
    fallback_model: str = "gpt-4.1-mini"
    fallback_api_key: Optional[str] = None

    # LLM Parameters
    temperature: float = 0.1
    max_tokens: int = 1000

    # Intent Detection Parameters
    max_intents_per_complaint: int = 5
    enable_custom_intents: bool = True

    # Summarization Parameters
    summary_min_length: int = 100
    summary_max_length: int = 300
    enable_noise_filtering: bool = True

    # Performance Configuration
    max_concurrent_requests: int = 10
    request_timeout_seconds: int = 30

    # Fallback Configuration
    min_content_length: int = 10

    @classmethod
    def from_environment(cls) -> "IntentDetectionConfig":
        """Load configuration from environment variables."""
        return cls(
            primary_api_key=os.getenv("GEMINI_API_KEY"),
            fallback_api_key=os.getenv("OPENAI_API_KEY"),
            temperature=float(os.getenv("INTENT_DETECTION_TEMPERATURE", "0.1")),
            max_concurrent_requests=int(os.getenv("MAX_CONCURRENT_INTENTS", "10")),
            enable_custom_intents=os.getenv("ENABLE_CUSTOM_INTENTS", "true").lower() == "true"
        )
```

## Monitoring and Metrics

### Intent Detection Metrics (Simplified)

```python
class IntentDetectionMetrics:
    """
    Comprehensive metrics collection for intent detection and summarization operations.
    Tracks performance for dual-task processing.
    """

    def __init__(self):
        self.processing_times = []
        self.intent_frequencies = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.model_usage = defaultdict(int)  # Track primary vs fallback usage
        self.summary_lengths = []  # Track summary quality metrics
        self.dual_task_success_rate = []  # Track successful dual-task completion

    def record_successful_analysis(self, result: IntentDetectionResult):
        """Record metrics for successful intent detection and summarization."""

        # Processing time
        self.processing_times.append(result.processing_time_ms)

        # Intent frequency tracking
        for intent in result.detected_intents:
            self.intent_frequencies[intent.intent_name] += 1

        # Summary quality metrics
        if result.content_summary:
            self.summary_lengths.append(len(result.content_summary))

        # Dual-task success tracking
        has_intents = len(result.detected_intents) > 0
        has_summary = bool(result.content_summary.strip())
        dual_task_success = has_intents and has_summary
        self.dual_task_success_rate.append(1 if dual_task_success else 0)

    def record_model_usage(self, model_name: str):
        """Record which model was used for analysis."""
        self.model_usage[model_name] += 1

    def record_error(self, error_type: str):
        """Record error occurrence by type."""
        self.error_counts[error_type] += 1

    def get_performance_summary(self) -> Dict[str, Any]:
        """Generate performance summary for monitoring."""

        if not self.processing_times:
            return {"status": "no_data"}

        return {
            "avg_processing_time_ms": statistics.mean(self.processing_times),
            "p95_processing_time_ms": statistics.quantile(self.processing_times, 0.95),
            "total_analyses": len(self.processing_times),
            "most_common_intents": dict(
                sorted(self.intent_frequencies.items(), key=lambda x: x[1], reverse=True)[:5]
            ),
            "model_usage_distribution": dict(self.model_usage),
            "error_distribution": dict(self.error_counts),
            "avg_summary_length": statistics.mean(self.summary_lengths) if self.summary_lengths else 0,
            "dual_task_success_rate": statistics.mean(self.dual_task_success_rate) if self.dual_task_success_rate else 0
        }
```

## Testing Strategy

### Unit Testing Approach

```python
from src.domain.entities.intent_type import IntentType

class TestIntentDetectionEngine:
    """
    Comprehensive test suite for intent detection and summarization engine using Agno framework.
    Tests both intent detection and content summarization functionality.
    Uses IntentType enum for consistent intent validation.
    """

    @pytest.mark.asyncio
    async def test_predefined_intent_detection(self):
        """Test detection of predefined Taiwan government intents."""

        test_cases = [
            {
                "content": "我要檢舉某公務員收受賄賂",
                "expected_intents": [IntentType.REPORT_VIOLATION.value]
            },
            {
                "content": "請協助我處理這個問題，服務態度很差",
                "expected_intents": [IntentType.REQUEST_ASSISTANCE.value, IntentType.DISSATISFIED_SERVICE_ATTITUDE.value]
            }
        ]

        for case in test_cases:
            result = await self.detect_intents_use_case.execute(
                IntentDetectionRequest(
                    complaint_id="test-001",
                    complaint_subject="測試",
                    complaint_content=case["content"]
                )
            )

            assert result.error_message is None
            assert len(result.detected_intents) >= len(case["expected_intents"])
            assert result.content_summary  # Should have generated summary
            assert 100 <= len(result.content_summary) <= 300  # Summary length validation

            detected_names = [intent.intent_name for intent in result.detected_intents]
            for expected_intent in case["expected_intents"]:
                assert expected_intent in detected_names

    @pytest.mark.asyncio
    async def test_custom_intent_detection(self):
        """Test detection of custom intents when predefined ones don't fit."""

        content = "我想了解新的數位身分證申請流程和相關法規"

        result = await self.detect_intents_use_case.execute(
            IntentDetectionRequest(
                complaint_id="test-002",
                complaint_subject="數位身分證諮詢",
                complaint_content=content
            )
        )

        assert result.error_message is None
        assert len(result.detected_intents) > 0
        assert result.content_summary  # Should have generated summary
        assert len(result.content_summary) >= 100  # Minimum summary length

        # Validate intent quality
        for intent in result.detected_intents:
            assert len(intent.reasoning) > 10
            assert len(intent.text_evidence) > 0
            assert isinstance(intent.is_predefined, bool)

    @pytest.mark.asyncio
    async def test_model_fallback(self):
        """Test fallback behavior when primary model fails."""

        # Mock primary model failure
        with patch.object(self.intent_analyzer, 'primary_agent') as mock_primary:
            mock_primary.run.side_effect = Exception("API Error")

            result = await self.detect_intents_use_case.execute(
                IntentDetectionRequest(
                    complaint_id="test-003",
                    complaint_subject="測試",
                    complaint_content="測試fallback機制"
                )
            )

            # Should still return valid result using fallback model
            assert result.error_message is None or "fallback" in result.error_message.lower()

    @pytest.mark.asyncio
    async def test_dual_task_completion(self):
        """Test that both intent detection and summarization complete successfully."""

        content = "我對於區公所辦理身分證換發的服務效率不滿，排隊等待時間過長，希望能夠改善流程，提高辦事效率。另外，承辦人員態度不夠友善，需要加強訓練。"

        result = await self.detect_intents_use_case.execute(
            IntentDetectionRequest(
                complaint_id="test-dual-task",
                complaint_subject="身分證辦理服務改善",
                complaint_content=content
            )
        )

        # Validate dual-task completion
        assert result.error_message is None
        assert len(result.detected_intents) > 0  # Intent detection success
        assert result.content_summary.strip()  # Summarization success

        # Validate expected intents using IntentType enum
        detected_names = [intent.intent_name for intent in result.detected_intents]
        expected_intents = [
            IntentType.COMPLAINT_PROBLEM.value,
            IntentType.REQUEST_IMPROVEMENT.value,
            IntentType.DISSATISFIED_SERVICE_ATTITUDE.value
        ]

        for expected_intent in expected_intents:
            assert expected_intent in detected_names

        # Validate summary quality
        assert 100 <= len(result.content_summary) <= 300
        assert "身分證" in result.content_summary  # Key content preserved
        assert "效率" in result.content_summary or "改善" in result.content_summary  # Core issue captured
```

## Deployment Considerations

### Production Configuration

```yaml
# Intent Detection Engine Configuration
intent_detection:
    model_settings:
        primary_model: "gemini-2.5-flash"
        fallback_model: "gpt-4.1-mini"
        temperature: 0.1
        max_tokens: 1000
        timeout_seconds: 30

    performance:
        max_concurrent_requests: 20

    thresholds:
        max_intents_per_complaint: 5
        min_content_length: 10

    fallback:
        error_retry_attempts: 2

    monitoring:
        metrics_collection: true
        performance_logging: true
        alert_on_error_rate: 0.05

# Environment Variables
environment:
    GEMINI_API_KEY: "your_gemini_api_key"
    OPENAI_API_KEY: "your_openai_api_key"
    AGNO_LOG_LEVEL: "INFO"
```

### Integration Validation

```python
async def validate_integration_health():
    """
    Health check for intent detection engine integration using Agno.
    """

    test_request = IntentDetectionRequest(
        complaint_id="health-check",
        complaint_subject="系統測試",
        complaint_content="測試陳情內容，請求協助解決問題"
    )

    try:
        result = await detect_intents_use_case.execute(test_request)

        # Validate expected behavior
        assert result.error_message is None
        assert len(result.detected_intents) > 0
        assert result.content_summary.strip()  # Summary generated
        assert result.processing_time_ms < 2000

        # Validate intent structure
        for intent in result.detected_intents:
            assert intent.intent_name
            assert intent.reasoning
            assert isinstance(intent.is_predefined, bool)

        # Validate summary quality
        assert 50 <= len(result.content_summary) <= 400  # Flexible length for health check

        return {
            "status": "healthy",
            "response_time_ms": result.processing_time_ms,
            "intents_detected": len(result.detected_intents),
            "summary_generated": bool(result.content_summary.strip()),
            "summary_length": len(result.content_summary)
        }

    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
```

### Dependencies

```toml
# pyproject.toml additions for Agno framework

[dependencies]
agno = "^1.7.9"  # Agno framework for multi-model agent support
google-generativeai = "^0.8.0"  # For Gemini 2.5 Flash
openai = "^1.45.0"  # For GPT-4.1 Mini fallback
```

---

**Document Version**: 3.0
**Created**: 2025-08-12
**Updated**: 2025-08-12
**Author**: Technical Design Specification
**Status**: Ready for Implementation with Dual-Task Processing (Intent Detection + Summarization) using Agno Framework
