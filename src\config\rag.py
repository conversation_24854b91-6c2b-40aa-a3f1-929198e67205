"""RAG (Retrieval-Augmented Generation) configuration settings."""

from pydantic import Field

from .base_llm_config import BaseConfig, SimilarityConfig
from .llm import LLMSettings


class RAGSettings(BaseConfig):
    """Configuration for RAG similarity search operations."""

    # Search Configuration
    max_candidates_per_index: int = Field(default=5, ge=1, le=20)
    search_timeout_seconds: float = Field(default=30.0, ge=1.0)
    enable_fallback_search: bool = Field(default=True)
    
    # Similarity Configuration
    similarity: SimilarityConfig = Field(default_factory=SimilarityConfig)
    
    # LLM Configuration for RAG
    llm_settings: LLMSettings = Field(default_factory=LLMSettings)
    
    # Generation Parameters
    max_context_length: int = Field(default=4000, ge=100, le=8000)
    response_max_tokens: int = Field(default=1000, ge=100, le=4096)
    
    def get_search_config(self) -> dict:
        """Get search configuration for RAG operations."""
        return {
            "max_candidates": self.max_candidates_per_index,
            "timeout": self.search_timeout_seconds,
            "enable_fallback": self.enable_fallback_search,
            "similarity_threshold": self.similarity.primary_similarity_threshold,
            "fallback_threshold": self.similarity.fallback_similarity_threshold,
        }
    
    def get_generation_config(self) -> dict:
        """Get generation configuration for RAG operations."""
        return {
            "max_context_length": self.max_context_length,
            "max_tokens": self.response_max_tokens,
            "temperature": self.llm_settings.temperature,
        }
