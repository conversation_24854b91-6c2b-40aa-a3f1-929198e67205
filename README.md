# AI RAG Application - Taiwan Government Complaint Classification

A Retrieval-Augmented Generation (RAG) application built with Clean Architecture, designed to process and classify Taiwan government complaint categories using semantic search capabilities.

## Project Overview

This AI RAG application processes Taiwan government complaint data, generating embeddings using Google's Gemini model and storing them in Elasticsearch for vector-based semantic search. The system is architected using Clean Architecture principles with clear separation of concerns across domain, application, and infrastructure layers.

## Technology Stack

- **Python 3.12**: Core language with modern async/await support
- **uv**: Package and environment management
- **Google Gemini embedding-001**: Advanced embedding generation
- **Elasticsearch 8.13.2**: Vector database for semantic search
- **Pydantic**: Data validation and settings management
- **Clean Architecture**: Domain-driven design with layered architecture

## Current Implementation Status

### ✅ Completed Features
- Complete data ingestion pipeline for Taiwan government complaint categories
- Google Gemini embedding generation with rate limiting
- Elasticsearch vector storage and indexing
- Comprehensive error handling and logging
- Environment configuration management
- Clean Architecture implementation with proper dependency injection

### ❌ Planned Features
- Search and retrieval functionality
- REST API endpoints for query processing
- Frontend interface for complaint classification
- Advanced query processing with hybrid search

## Project Structure

```
src/
├── domain/                     # Domain Layer - Core Business Logic
│   ├── entities/              # Business Entities
│   │   ├── main_category.py   # Main complaint category entity
│   │   └── sub_category.py    # Sub complaint category entity
│   └── interfaces/            # Abstract Interfaces
│       ├── embedder_interface.py
│       └── repository_interface.py
│
├── application/               # Application Layer - Use Cases
│   └── use_cases/
│       └── process_categories.py  # Main processing workflow
│
├── infrastructure/            # Infrastructure Layer - External Integrations
│   ├── embedders/
│   │   └── gemini_embedder.py     # Google Gemini API integration
│   ├── repositories/
│   │   └── elasticsearch_repository.py  # Elasticsearch operations
│   └── exceptions.py          # Custom exception handling
│
├── config/
│   └── settings.py           # Application configuration
├── data/
│   └── categories_def.json   # Taiwan complaint categories data
└── main.py                   # Application entry point

run.py                        # Main execution script
pyproject.toml               # Project configuration and dependencies
requirements.txt             # Alternative dependency specification
```

## Setup Instructions

### 1. Prerequisites

- Python 3.12
- Elasticsearch 8.13.2
- Google Gemini API key

### 2. Install Dependencies

Using uv (recommended):
```bash
uv sync
```

Alternative using pip:
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration

Create a `.env` file in the project root:

```env
# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# Optional: Custom Index Names
MAIN_CATEGORIES_INDEX=main_categories
SUB_CATEGORIES_INDEX=sub_categories

# Optional: Embedding Configuration
EMBEDDING_DIMS=3072
EMBEDDING_TASK_TYPE=RETRIEVAL_DOCUMENT
```

### 4. Start Elasticsearch

Using Docker:
```bash
docker run -d --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  -e "xpack.security.enabled=false" \
  elasticsearch:8.13.2
```

Verify Elasticsearch is running:
```bash
curl http://localhost:9200
```

## Usage

### Run the Application

```bash
uv run run.py
```

This command will:
1. Create Elasticsearch indices (`main_categories`, `sub_categories`)
2. Load complaint categories from `src/data/categories_def.json`
3. Generate embeddings for main and sub categories using Gemini
4. Store processed data with embeddings in Elasticsearch

### Alternative Execution

```bash
uv run src/main.py
```

### Processing Output

The application provides detailed progress information:
```
=== RAG系統 - 市民陳情案件分類 ===
創建Elasticsearch索引...
生成XX個主案類的embeddings...
生成XX個子案類的embeddings...
儲存主案類到Elasticsearch...
儲存子案類到Elasticsearch...
✅ 處理完成！
```

## Data Architecture

### Embedding Strategy

**Main Categories**: `主案類: [Category Name]\n描述: [Description]`

**Sub Categories**: `子案類: [Sub Category]\n描述: [Sub Description]\n所屬主案類: [Main Category]`

### Elasticsearch Schema

#### Main Categories Index
```json
{
  "main_category": "string (keyword)",
  "description": "string (text)",
  "combined_text": "string (text)",
  "sub_categories": ["array of strings (keyword)"],
  "embedding": [3072 dimensions (dense_vector)],
  "created_at": "timestamp (yyyy/MM/dd HH:mm:ss)"
}
```

#### Sub Categories Index
```json
{
  "main_category": "string (keyword)",
  "sub_category": "string (keyword)",
  "sub_description": "string (text)",
  "main_description": "string (text)",
  "combined_text": "string (text)",
  "keywords": ["array of strings (keyword)"],
  "embedding": [3072 dimensions (dense_vector)],
  "created_at": "timestamp (yyyy/MM/dd HH:mm:ss)"
}
```

## Development

### Architecture Principles

This application follows Clean Architecture principles:

1. **Domain Layer**: Contains business entities and interfaces, independent of external frameworks
2. **Application Layer**: Implements use cases and orchestrates business logic
3. **Infrastructure Layer**: Handles external integrations (Gemini API, Elasticsearch)
4. **Dependency Inversion**: Higher-level modules don't depend on lower-level modules

### Adding Dependencies

```bash
uv add package-name
```

### Running Tests

```bash
# Install test dependencies
uv add pytest pytest-asyncio --dev

# Run tests
uv run pytest
```

## Configuration

The application uses Pydantic Settings for configuration management. Key settings include:

- **GEMINI_API_KEY**: Required for embedding generation
- **ELASTICSEARCH_URL**: Elasticsearch cluster endpoint
- **EMBEDDING_DIMS**: Vector dimensions (default: 3072)
- **EMBEDDING_TASK_TYPE**: Gemini task type (default: RETRIEVAL_DOCUMENT)

## Error Handling

The application includes comprehensive error handling:

- **ElasticsearchConnectionError**: Handles connection issues with suggestions
- **ElasticsearchBulkError**: Provides detailed information about failed bulk operations
- **Rate Limiting**: Built-in delays to respect Gemini API limits
- **Graceful Shutdown**: Proper cleanup of connections and resources

## Known Limitations

1. **API Quotas**: Google Gemini API has rate limits and quotas
2. **Processing Limits**: Current implementation processes limited batches for demonstration
3. **Search Functionality**: Retrieval and search capabilities are not yet implemented
4. **Scalability**: Single-node Elasticsearch configuration for development

## Troubleshooting

### Common Issues

**Elasticsearch Connection Failed**
- Verify Elasticsearch is running on the configured port
- Check network connectivity and firewall settings
- Ensure Elasticsearch version compatibility (8.13.2 recommended)

**Gemini API Errors**
- Verify API key is valid and has sufficient quota
- Check API rate limits and quotas in Google Cloud Console
- Ensure proper internet connectivity

**Embedding Generation Slow**
- API rate limiting is intentionally implemented
- Consider adjusting batch sizes for optimal performance
- Monitor API quota usage

## Contributing

1. Follow Clean Architecture principles
2. Ensure comprehensive error handling
3. Add appropriate logging
4. Update tests for new functionality
5. Maintain backward compatibility

## License

This project is for educational and research purposes.