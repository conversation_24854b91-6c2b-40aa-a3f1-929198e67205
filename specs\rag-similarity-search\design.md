# Technical Design: RAG Similarity Search

## 1. Architecture Overview

The RAG Similarity Search feature extends the existing Clean Architecture to provide vector-based semantic similarity retrieval. It integrates seamlessly with the current infrastructure while adding specialized search capabilities for complaint classification.

### Architecture Layers

```
┌─────────────────────────────────────────────────────────────┐
│                     Application Layer                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              ClassifyComplaintsUseCase                  │ │
│  │  (depends on → RAGRetrieverInterface)                   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Domain Layer                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              RAGRetrieverInterface                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │  async def search_similar_categories(               │ │ │
│  │  │      query_text: str,                               │ │ │
│  │  │      similarity_threshold: float = 0.75,            │ │ │
│  │  │      fallback_threshold: float = 0.6,               │ │ │
│  │  │      max_results: int = 5                           │ │ │
│  │  │  ) -> RAGSearchResult                               │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                RAGSearchResult                          │ │
│  │  - candidates: List[RAGCandidate]                       │ │
│  │  - used_fallback_threshold: bool                        │ │
│  │  - search_metadata: RAGSearchMetadata                   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            RAGElasticsearchRetriever                    │ │
│  │  (extends → ElasticsearchRepository)                    │ │
│  │  (uses → GeminiEmbedder)                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. Domain Layer Design

### RAGRetrieverInterface

**File**: `src/domain/interfaces/rag_retriever.py`

```python
from abc import ABC, abstractmethod
from typing import List
from ..entities.rag_search import RAGSearchResult

class RAGRetrieverInterface(ABC):
    """Interface for semantic similarity search in complaint categories."""
    
    @abstractmethod
    async def search_similar_categories(
        self,
        query_text: str,
        similarity_threshold: float = 0.75,
        fallback_threshold: float = 0.6,
        max_results: int = 5
    ) -> RAGSearchResult:
        """
        Search for categories similar to the given query text.
        
        Args:
            query_text: Complaint text to find similar categories for
            similarity_threshold: Primary cosine similarity threshold (0.75)
            fallback_threshold: Fallback threshold when primary fails (0.6)
            max_results: Maximum candidates to return per index
            
        Returns:
            RAGSearchResult with ranked candidates and metadata
            
        Raises:
            ValueError: If query_text is empty or invalid thresholds
            ElasticsearchConnectionError: If Elasticsearch is unavailable
        """
        pass
```

### Enhanced RAGSearchResult Entity

**File**: `src/domain/entities/rag_search.py`

```python
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class RAGSearchMetadata(BaseModel):
    """Metadata about the similarity search operation."""
    
    search_timestamp: datetime = Field(default_factory=datetime.now)
    embedding_generation_time_ms: float
    elasticsearch_query_time_ms: float
    total_search_time_ms: float
    primary_threshold_used: float
    fallback_threshold_used: Optional[float] = None
    total_candidates_found: int
    sub_categories_searched: int
    main_categories_searched: int

class RAGSearchResult(BaseModel):
    """Result container for similarity search operations."""
    
    candidates: List[RAGCandidate] = Field(default_factory=list)
    used_fallback_threshold: bool = False
    search_metadata: RAGSearchMetadata
    
    @property
    def has_high_confidence_matches(self) -> bool:
        """True if any candidates found with primary threshold."""
        return not self.used_fallback_threshold and len(self.candidates) > 0
    
    @property
    def best_candidate(self) -> Optional[RAGCandidate]:
        """Returns the highest scoring candidate, if any."""
        return self.candidates[0] if self.candidates else None
```

### Enhanced RAGCandidate Entity

**File**: `src/domain/entities/rag_search.py` (addition)

```python
class RAGCandidate(BaseModel):
    """Enhanced candidate with similarity search context."""
    
    category_id: str
    similarity_score: float = Field(ge=0.0, le=1.0)
    category_data: dict
    index_source: str = Field(description="'sub_categories' or 'main_categories'")
    
    # Enhanced fields for classification
    category_type: str = Field(description="Type extracted from category_data")
    keywords: List[str] = Field(default_factory=list)
    combined_text: str = Field(description="Text used for embedding generation")
    
    @property
    def is_high_confidence(self) -> bool:
        """True if similarity score meets high confidence threshold."""
        return self.similarity_score >= 0.75
    
    @property
    def confidence_level(self) -> str:
        """Returns 'high', 'medium', or 'low' based on similarity score."""
        if self.similarity_score >= 0.75:
            return "high"
        elif self.similarity_score >= 0.6:
            return "medium"
        else:
            return "low"
```

## 3. Infrastructure Layer Implementation

### RAGElasticsearchRetriever

**File**: `src/infrastructure/rag_elasticsearch_retriever.py`

```python
import asyncio
import time
from typing import List, Dict, Any, Optional
from elasticsearch import AsyncElasticsearch

from ..repositories.elasticsearch_repository import ElasticsearchRepository
from ..embedders.gemini_embedder import GeminiEmbedder
from ...domain.interfaces.rag_retriever import RAGRetrieverInterface
from ...domain.entities.rag_search import RAGSearchResult, RAGSearchMetadata, RAGCandidate
from ...domain.exceptions import ElasticsearchConnectionError

class RAGElasticsearchRetriever(ElasticsearchRepository, RAGRetrieverInterface):
    """Elasticsearch-based implementation of RAG similarity search."""
    
    def __init__(self, client: AsyncElasticsearch, embedder: GeminiEmbedder):
        super().__init__(client)
        self._embedder = embedder
    
    async def search_similar_categories(
        self,
        query_text: str,
        similarity_threshold: float = 0.75,
        fallback_threshold: float = 0.6,
        max_results: int = 5
    ) -> RAGSearchResult:
        """
        Implementation of semantic similarity search.
        
        Algorithm:
        1. Validate input parameters
        2. Generate query embedding using GeminiEmbedder
        3. Execute parallel cosine similarity queries on both indices
        4. Apply primary threshold filtering
        5. If no results, retry with fallback threshold
        6. Rank and limit results
        7. Return structured RAGSearchResult with metadata
        """
        start_time = time.time()
        
        # Input validation
        if not query_text or not query_text.strip():
            raise ValueError("Query text cannot be empty")
        
        if not 0.0 <= fallback_threshold <= similarity_threshold <= 1.0:
            raise ValueError("Invalid threshold values")
        
        # Step 1: Generate embedding
        embedding_start = time.time()
        try:
            query_embedding = await self._embedder.generate_embedding(
                query_text, 
                task_type="RETRIEVAL_QUERY"
            )
        except Exception as e:
            raise ElasticsearchConnectionError(f"Embedding generation failed: {str(e)}")
        
        embedding_time = (time.time() - embedding_start) * 1000
        
        # Step 2: Execute parallel similarity searches
        es_start = time.time()
        sub_categories_task = self._search_index_similarity(
            "sub_categories", query_embedding, similarity_threshold, max_results
        )
        main_categories_task = self._search_index_similarity(
            "main_categories", query_embedding, similarity_threshold, max_results
        )
        
        sub_results, main_results = await asyncio.gather(
            sub_categories_task, main_categories_task, return_exceptions=True
        )
        
        # Handle search exceptions
        if isinstance(sub_results, Exception):
            raise ElasticsearchConnectionError(f"Sub-categories search failed: {str(sub_results)}")
        if isinstance(main_results, Exception):
            raise ElasticsearchConnectionError(f"Main-categories search failed: {str(main_results)}")
        
        es_time = (time.time() - es_start) * 1000
        
        # Step 3: Combine and filter results
        all_candidates = sub_results + main_results
        used_fallback = False
        
        # Step 4: Apply fallback if needed
        if not all_candidates:
            used_fallback = True
            sub_fallback_task = self._search_index_similarity(
                "sub_categories", query_embedding, fallback_threshold, max_results
            )
            main_fallback_task = self._search_index_similarity(
                "main_categories", query_embedding, fallback_threshold, max_results
            )
            
            sub_fallback, main_fallback = await asyncio.gather(
                sub_fallback_task, main_fallback_task, return_exceptions=True
            )
            
            if not isinstance(sub_fallback, Exception):
                all_candidates.extend(sub_fallback)
            if not isinstance(main_fallback, Exception):
                all_candidates.extend(main_fallback)
        
        # Step 5: Rank and limit final results
        final_candidates = sorted(
            all_candidates, 
            key=lambda x: x.similarity_score, 
            reverse=True
        )[:max_results * 2]  # Allow more candidates for better selection
        
        total_time = (time.time() - start_time) * 1000
        
        # Step 6: Create metadata
        metadata = RAGSearchMetadata(
            embedding_generation_time_ms=embedding_time,
            elasticsearch_query_time_ms=es_time,
            total_search_time_ms=total_time,
            primary_threshold_used=similarity_threshold,
            fallback_threshold_used=fallback_threshold if used_fallback else None,
            total_candidates_found=len(final_candidates),
            sub_categories_searched=len([c for c in final_candidates if c.index_source == "sub_categories"]),
            main_categories_searched=len([c for c in final_candidates if c.index_source == "main_categories"])
        )
        
        return RAGSearchResult(
            candidates=final_candidates,
            used_fallback_threshold=used_fallback,
            search_metadata=metadata
        )
    
    async def _search_index_similarity(
        self,
        index_name: str,
        query_embedding: List[float],
        threshold: float,
        max_results: int
    ) -> List[RAGCandidate]:
        """Execute cosine similarity search on a specific index."""
        
        query = {
            "size": max_results,
            "min_score": threshold,
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding}
                    }
                }
            },
            "_source": ["category_id", "main_category", "sub_category", "keywords", "combined_text"]
        }
        
        try:
            response = await self.client.search(index=index_name, body=query)
            candidates = []
            
            for hit in response["hits"]["hits"]:
                # Normalize score back to 0-1 range (script_score adds 1.0)
                similarity_score = hit["_score"] - 1.0
                
                candidate = RAGCandidate(
                    category_id=hit["_source"]["category_id"],
                    similarity_score=round(similarity_score, 4),
                    category_data=hit["_source"],
                    index_source=index_name,
                    category_type=hit["_source"].get("sub_category" if index_name == "sub_categories" else "main_category", ""),
                    keywords=hit["_source"].get("keywords", []),
                    combined_text=hit["_source"].get("combined_text", "")
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            # Return empty list for individual index failures to allow graceful degradation
            return []
```

## 4. Configuration Integration

### Enhanced Settings

**File**: `src/config/settings.py` (additions)

```python
class RAGSearchSettings(BaseModel):
    """Configuration for RAG similarity search operations."""
    
    primary_similarity_threshold: float = Field(default=0.75, ge=0.0, le=1.0)
    fallback_similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)
    max_candidates_per_index: int = Field(default=5, ge=1, le=20)
    search_timeout_seconds: float = Field(default=30.0, ge=1.0)
    enable_fallback_search: bool = Field(default=True)
    
    @validator('fallback_similarity_threshold')
    def fallback_must_be_lower(cls, v, values):
        if 'primary_similarity_threshold' in values and v >= values['primary_similarity_threshold']:
            raise ValueError('Fallback threshold must be lower than primary threshold')
        return v

# Add to main Settings class
class Settings(BaseSettings):
    # ... existing fields ...
    rag_search: RAGSearchSettings = Field(default_factory=RAGSearchSettings)
```

## 5. API Specifications

### Primary Search Method

```python
async def search_similar_categories(
    query_text: str,
    similarity_threshold: float = 0.75,
    fallback_threshold: float = 0.6,
    max_results: int = 5
) -> RAGSearchResult
```

**Parameters:**
- `query_text`: Traditional Chinese complaint text (required, non-empty)
- `similarity_threshold`: Primary cosine similarity threshold (0.0-1.0, default 0.75)
- `fallback_threshold`: Fallback threshold for low-confidence matching (0.0-1.0, default 0.6)
- `max_results`: Maximum candidates per index to return (1-20, default 5)

**Returns:**
- `RAGSearchResult`: Structured result with candidates, fallback status, and detailed metadata

**Raises:**
- `ValueError`: Invalid input parameters
- `ElasticsearchConnectionError`: Elasticsearch connectivity or query failures

### Query Optimization Strategy

**Elasticsearch Query Pattern:**
```json
{
  "size": 5,
  "min_score": 0.75,
  "query": {
    "script_score": {
      "query": {"match_all": {}},
      "script": {
        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
        "params": {"query_vector": [... 3072 dimensions ...]}
      }
    }
  },
  "_source": ["category_id", "main_category", "sub_category", "keywords", "combined_text"]
}
```

## 6. Performance Optimizations

### Concurrent Search Strategy

```python
# Execute parallel searches on both indices
async def parallel_index_search():
    tasks = [
        search_index_similarity("sub_categories", embedding, threshold, max_results),
        search_index_similarity("main_categories", embedding, threshold, max_results)
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

### Memory Optimization

- **Embedding Caching**: Consider caching for repeated identical queries
- **Result Limiting**: Strict limits on candidates per index to control memory usage
- **Lazy Loading**: Only load required source fields from Elasticsearch

### Query Performance

- **Index Optimization**: Leverage existing dense vector indices optimized for cosine similarity
- **Score Thresholding**: Use `min_score` to filter at Elasticsearch level
- **Source Filtering**: Only retrieve necessary fields to minimize network transfer

## 7. Error Handling Strategy

### Exception Hierarchy

```python
# Existing exceptions to reuse
ElasticsearchConnectionError  # Network/connectivity issues
ElasticsearchBulkError       # Bulk operation failures

# New exceptions to add
class RAGSearchError(Exception):
    """Base exception for RAG search operations."""
    pass

class EmbeddingGenerationError(RAGSearchError):
    """Failed to generate query embeddings."""
    pass

class SimilaritySearchError(RAGSearchError):
    """Failed similarity search operation."""
    pass
```

### Graceful Degradation

```python
async def resilient_search():
    try:
        # Primary search attempt
        return await primary_search()
    except ElasticsearchConnectionError:
        # Log error, attempt fallback if configured
        if enable_fallback:
            return await fallback_search()
        raise
    except EmbeddingGenerationError:
        # Cannot proceed without embeddings
        raise
```

## 8. Integration Points

### With Existing GeminiEmbedder

```python
# Reuse existing embedder instance
embedder = GeminiEmbedder(api_key=settings.gemini_api_key)

# Use established embedding generation pattern
query_embedding = await embedder.generate_embedding(
    text=query_text,
    task_type="RETRIEVAL_QUERY"  # Specific task type for queries
)
```

### With Existing ElasticsearchRepository

```python
# Extend repository following established patterns
class RAGElasticsearchRetriever(ElasticsearchRepository, RAGRetrieverInterface):
    def __init__(self, client: AsyncElasticsearch, embedder: GeminiEmbedder):
        super().__init__(client)  # Inherit connection management
        self._embedder = embedder
```

### With Application Layer

```python
# Use case integration pattern
class ClassifyComplaintsUseCase:
    def __init__(self, rag_retriever: RAGRetrieverInterface):
        self._rag_retriever = rag_retriever
    
    async def classify_complaint(self, complaint_text: str) -> ClassificationResult:
        # Step 1: Get similar categories via RAG search
        search_result = await self._rag_retriever.search_similar_categories(complaint_text)
        
        # Step 2: Use candidates for LLM-based classification
        return await self._perform_classification(complaint_text, search_result.candidates)
```

## 9. Testing Strategy

### Unit Tests

```python
# Mock-based testing for retriever
@pytest.mark.asyncio
async def test_search_similar_categories_success():
    mock_embedder = AsyncMock()
    mock_es_client = AsyncMock()
    
    retriever = RAGElasticsearchRetriever(mock_es_client, mock_embedder)
    
    # Test successful search with high confidence results
    result = await retriever.search_similar_categories("道路坑洞問題")
    
    assert len(result.candidates) > 0
    assert result.candidates[0].similarity_score >= 0.75
    assert not result.used_fallback_threshold
```

### Integration Tests

```python
# Real Elasticsearch integration
@pytest.mark.integration
async def test_rag_search_end_to_end():
    # Use test Elasticsearch instance with sample data
    retriever = setup_test_retriever()
    
    result = await retriever.search_similar_categories("交通號誌故障")
    
    # Validate results match expected categories
    assert "交通設施" in [c.category_type for c in result.candidates]
    assert all(c.similarity_score >= 0.6 for c in result.candidates)
```

### Performance Tests

```python
@pytest.mark.performance
async def test_search_latency_target():
    retriever = setup_performance_test_retriever()
    
    start_time = time.time()
    result = await retriever.search_similar_categories("環境污染檢舉")
    search_time = (time.time() - start_time) * 1000
    
    # Verify 500ms target (excluding embedding time)
    assert result.search_metadata.elasticsearch_query_time_ms < 500
```

## 10. Deployment Considerations

### Configuration Management

```python
# Environment-specific configurations
PRODUCTION_RAG_CONFIG = {
    "primary_similarity_threshold": 0.75,
    "fallback_similarity_threshold": 0.6,
    "max_candidates_per_index": 5,
    "search_timeout_seconds": 30.0
}

DEVELOPMENT_RAG_CONFIG = {
    "primary_similarity_threshold": 0.7,  # More lenient for testing
    "fallback_similarity_threshold": 0.5,
    "max_candidates_per_index": 10,  # More candidates for analysis
    "search_timeout_seconds": 60.0
}
```

### Monitoring Integration

```python
# Metrics collection points
async def instrumented_search(query_text: str) -> RAGSearchResult:
    with metrics.timer("rag_search_duration"):
        result = await search_similar_categories(query_text)
        
        # Record metrics
        metrics.histogram("similarity_scores", [c.similarity_score for c in result.candidates])
        metrics.counter("fallback_threshold_used", result.used_fallback_threshold)
        metrics.gauge("candidates_returned", len(result.candidates))
        
        return result
```

## 11. Future Extensibility

### Pluggable Similarity Algorithms

```python
# Interface for future similarity strategies
class SimilarityStrategy(ABC):
    @abstractmethod
    async def calculate_similarity(
        self, 
        query_embedding: List[float], 
        candidate_embedding: List[float]
    ) -> float:
        pass

# Current implementation
class CosineSimilarityStrategy(SimilarityStrategy):
    async def calculate_similarity(self, query_emb, candidate_emb) -> float:
        # Elasticsearch cosine similarity implementation
        pass
```

### Multi-Modal Search Support

```python
# Future extension point for multi-modal queries
class MultiModalRAGRetriever(RAGRetrieverInterface):
    async def search_similar_categories(
        self,
        query_text: str,
        query_metadata: Optional[Dict] = None,  # Future: location, urgency, etc.
        **kwargs
    ) -> RAGSearchResult:
        # Implementation combining text + metadata similarity
        pass
```

This technical design provides a comprehensive implementation plan that seamlessly integrates with the existing Clean Architecture while delivering the vector similarity search capabilities required for the complaint classification system.