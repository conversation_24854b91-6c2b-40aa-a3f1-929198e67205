import logging
from typing import Optional
from ...domain.interfaces.rag_retriever import RAGRetrieverInterface
from ...domain.entities.rag_search import RAGSearchResult
from ...config.settings import Settings

# 設定日誌
logger = logging.getLogger(__name__)


class SimilaritySearchUseCase:
    """
    Use case for performing semantic similarity search on complaint categories.
    
    Orchestrates the RAG similarity search workflow by coordinating
    embedding generation and vector similarity matching.
    """
    
    def __init__(self, rag_retriever: RAGRetrieverInterface, settings: Settings):
        """
        Initialize the similarity search use case.
        
        Args:
            rag_retriever: Implementation of RAG retriever interface
            settings: Application settings with RAG search configuration
        """
        self._rag_retriever = rag_retriever
        self._settings = settings
    
    async def search_similar_categories(
        self,
        query_text: str,
        similarity_threshold: Optional[float] = None,
        fallback_threshold: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> RAGSearchResult:
        """
        Search for categories similar to the given query text.
        
        Args:
            query_text: Complaint text to find similar categories for
            similarity_threshold: Override primary threshold (uses config default if None)
            fallback_threshold: Override fallback threshold (uses config default if None)  
            max_results: Override max results (uses config default if None)
            
        Returns:
            RAGSearchResult with ranked candidates and metadata
            
        Raises:
            ValueError: If query_text is empty or invalid parameters
            ElasticsearchConnectionError: If Elasticsearch is unavailable
        """
        # Use configuration defaults if not provided
        effective_similarity_threshold = (
            similarity_threshold or self._settings.rag_search.primary_similarity_threshold
        )
        effective_fallback_threshold = (
            fallback_threshold or self._settings.rag_search.fallback_similarity_threshold
        )
        effective_max_results = (
            max_results or self._settings.rag_search.max_candidates_per_index
        )
        
        logger.info(
            f"Starting similarity search: query='{query_text[:50]}...', "
            f"threshold={effective_similarity_threshold}, "
            f"fallback={effective_fallback_threshold}, "
            f"max_results={effective_max_results}"
        )
        
        try:
            # Delegate to RAG retriever
            search_result = await self._rag_retriever.search_similar_categories(
                query_text=query_text,
                similarity_threshold=effective_similarity_threshold,
                fallback_threshold=effective_fallback_threshold,
                max_results=effective_max_results
            )
            
            # Log results summary
            metadata = search_result.search_metadata
            logger.info(
                f"Similarity search completed: {len(search_result.candidates)} candidates found, "
                f"total_time={metadata.total_search_time_ms:.2f}ms, "
                f"used_fallback={search_result.used_fallback_threshold}"
            )
            
            return search_result
            
        except Exception as e:
            logger.error(f"Similarity search failed: {str(e)}")
            raise
    
    async def search_with_expanded_results(
        self, 
        query_text: str, 
        max_results: int = 10
    ) -> RAGSearchResult:
        """
        Search with expanded result set for comprehensive analysis.
        
        Args:
            query_text: Complaint text to find similar categories for
            max_results: Maximum number of results to return (up to 20)
            
        Returns:
            RAGSearchResult with expanded candidate list
        """
        # Ensure max_results doesn't exceed system limits
        effective_max_results = min(max_results, 20)
        
        return await self.search_similar_categories(
            query_text=query_text,
            max_results=effective_max_results
        )