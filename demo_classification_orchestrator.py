"""
Demonstration script for the Classification Logic Orchestrator.

This script showcases the complete complaint classification workflow including:
1. Intent detection and summarization
2. RAG similarity search
3. LLM-based classification with multi-provider support
4. Batch processing capabilities

Usage:
    uv run demo_classification_orchestrator.py
"""

import asyncio
import logging
import time

from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config import Settings
from src.domain.entities.complaint_input import ComplaintInput
from src.domain.entities.confidence_level import ConfidenceLevel
from src.infrastructure.embedders.gemini_embedder import GeminiEmbedder
from src.infrastructure.rag_elasticsearch_retriever import RAGElasticsearchRetriever

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Sample complaint data for demonstration
SAMPLE_COMPLAINTS = [
    {
        "case_id": "DEMO-001",
        "subject": "交通號誌故障",
        "content": "台北市信義區基隆路與忠孝東路口的紅綠燈故障了，已經三天沒有正常運作，造成交通大亂。請儘速派人維修，這個路口車流量很大，很危險。",
    },
    # {
    #     "case_id": "DEMO-002",
    #     "subject": "路面坑洞問題",
    #     "content": "新北市板橋區中山路二段有很大的坑洞，下雨後積水很深，機車經過很容易摔倒。已經發生好幾起事故了，希望盡快修補。",
    # },
    # {
    #     "case_id": "DEMO-003",
    #     "subject": "噪音污染檢舉",
    #     "content": "隔壁工廠晚上十二點後還在施工，機器聲音很大，影響附近居民睡眠。請環保局來檢查並開罰單，這種情況已經持續一個月了。",
    # },
    # {
    #     "case_id": "DEMO-004",
    #     "subject": "違規停車檢舉",
    #     "content": "有車輛長期佔用身障車位，車牌號碼是ABC-1234，停在台北市大安區敦化南路某大樓前。請交通執法單位前來取締。",
    # },
    # {
    #     "case_id": "DEMO-005",
    #     "subject": "水溝蓋損壞",
    #     "content": "社區門口的水溝蓋破損，有安全疑慮。老人家和小孩經過很容易跌倒受傷。希望相關單位盡快更換新的水溝蓋。",
    # },
]


async def create_orchestrator(settings: Settings) -> ClassifyComplaintsUseCase:
    """Create and configure the classification orchestrator."""

    # Get configuration manager for centralized config management
    config_manager = get_config_manager()

    # Create intent detection use case with new config system
    intent_config = config_manager.get_intent_detection_config()
    detect_intents_use_case = DetectIntentsUseCase(intent_config)

    # Create embedder for RAG retriever
    embedder = GeminiEmbedder(settings)

    # Create similarity search use case
    rag_retriever = RAGElasticsearchRetriever(settings, embedder)
    similarity_search_use_case = SimilaritySearchUseCase(rag_retriever, settings)

    # Create main orchestrator with new config system
    classification_config = config_manager.get_classification_config()
    orchestrator = ClassifyComplaintsUseCase(
        detect_intents_use_case=detect_intents_use_case,
        similarity_search_use_case=similarity_search_use_case,
        classification_config=classification_config,
        enable_llm_analysis=classification_config.enable_llm_analysis,
    )

    return orchestrator


async def demo_single_classification(orchestrator: ClassifyComplaintsUseCase):
    """Demonstrate single complaint classification."""

    print("\n" + "=" * 80)
    print("🔍 單一陳情案件分類示範")
    print("=" * 80)

    # Use the first sample complaint
    complaint_data = SAMPLE_COMPLAINTS[0]
    complaint = ComplaintInput(**complaint_data)

    print("\n📝 陳情案件資訊:")
    print(f"   案件編號: {complaint.case_id}")
    print(f"   主旨: {complaint.subject}")
    print(f"   內容: {complaint.content}")

    print("\n⏳ 開始執行分類流程...")
    start_time = time.time()

    try:
        # Execute classification
        result = await orchestrator.execute(complaint)

        end_time = time.time()
        execution_time = (end_time - start_time) * 1000

        print(f"\n✅ 分類完成! (執行時間: {execution_time:.0f}ms)")
        print("\n📊 分類結果:")
        print(f"   主類別: {result.main_category}")
        print(f"   子類別: {result.sub_category}")
        print(f"   信心度: {result.confidence}")
        print(f"   相似度分數: {result.similarity_score:.3f}")
        print(f"   檢測到的意圖: {len(result.intents)} 個")
        for intent in result.intents:
            print(f"     - {intent}")
        print(f"   分類理由: {result.reasoning}")

        # Show RAG candidates
        if result.rag_candidates:
            print("\n🎯 相似類別候選 (前3名):")
            for i, candidate in enumerate(result.rag_candidates[:3], 1):
                print(
                    f"   {i}. {candidate.sub_category} (相似度: {candidate.similarity_score:.3f})"
                )

    except Exception as e:
        print(f"\n❌ 分類失敗: {str(e)}")
        logger.error(f"Classification failed: {e}", exc_info=True)


async def demo_batch_classification(orchestrator: ClassifyComplaintsUseCase):
    """Demonstrate batch complaint classification."""

    print("\n" + "=" * 80)
    print("📦 批次陳情案件分類示範")
    print("=" * 80)

    # Create complaint inputs
    complaints = [ComplaintInput(**data) for data in SAMPLE_COMPLAINTS]

    print(f"\n📝 準備處理 {len(complaints)} 件陳情案件")
    for complaint in complaints:
        print(f"   - {complaint.case_id}: {complaint.subject}")

    print("\n⏳ 開始批次處理...")
    start_time = time.time()

    try:
        # Execute batch classification
        results = await orchestrator.execute_batch(
            complaints,
            concurrency_limit=3,  # Process 3 complaints concurrently
        )

        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        avg_time_per_complaint = total_time / len(results)

        print("\n✅ 批次處理完成!")
        print(f"   總時間: {total_time:.0f}ms")
        print(f"   平均每件: {avg_time_per_complaint:.0f}ms")

        # Show results summary
        print("\n📊 批次處理結果摘要:")
        high_confidence_count = sum(
            1 for r in results if r.confidence == ConfidenceLevel.HIGH
        )

        print(f"   成功處理: {len(results)} 件")
        print(
            f"   高信心度: {high_confidence_count} 件 ({high_confidence_count / len(results) * 100:.1f}%)"
        )

        # Show detailed results
        print("\n📋 詳細分類結果:")
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result.case_id}")
            print(f"      類別: {result.main_category} > {result.sub_category}")
            print(
                f"      信心度: {result.confidence} (相似度: {result.similarity_score:.3f})"
            )
            print(f"      處理時間: {result.processing_time_ms}ms")
            print()

    except Exception as e:
        print(f"\n❌ 批次處理失敗: {str(e)}")
        logger.error(f"Batch classification failed: {e}", exc_info=True)


async def demo_performance_metrics(orchestrator: ClassifyComplaintsUseCase):
    """Demonstrate performance metrics collection."""

    print("\n" + "=" * 80)
    print("📈 效能指標示範")
    print("=" * 80)

    # Get performance metrics
    metrics = orchestrator.get_performance_metrics()

    if metrics.get("status") == "no_data":
        print("\n⚠️  尚無效能資料 (請先執行分類操作)")
        return

    print("\n📊 整體效能統計:")
    print(f"   已處理案件: {metrics['total_processed']} 件")
    print(f"   成功率: {metrics['success_rate'] * 100:.1f}%")
    print(f"   高信心度比例: {metrics['high_confidence_rate'] * 100:.1f}%")

    print("\n⏱️  時間統計 (毫秒):")
    print(f"   平均總處理時間: {metrics['avg_total_time_ms']:.0f}ms")
    print(f"   平均意圖檢測時間: {metrics['avg_intent_time_ms']:.0f}ms")
    print(f"   平均RAG搜尋時間: {metrics['avg_rag_time_ms']:.0f}ms")
    print(f"   平均LLM分類時間: {metrics['avg_llm_time_ms']:.0f}ms")


async def main():
    """Main demonstration function."""

    print("🚀 AI RAG 陳情分類系統 - 完整工作流程示範")
    print("=" * 80)

    try:
        # Load settings
        print("\n⚙️  載入系統設定...")
        settings = Settings()
        config_manager = get_config_manager()
        classification_config = config_manager.get_classification_config()

        print(f"   LLM 提供者: {classification_config.llm_provider}")
        print(
            f"   LLM 分析 (意圖 + 摘要): {'啟用' if classification_config.enable_llm_analysis else '停用'}"
        )

        # Create orchestrator
        print("\n🔧 初始化分類協調器...")
        orchestrator = await create_orchestrator(settings)
        print("   協調器初始化完成")

        # Demo single classification
        await demo_single_classification(orchestrator)

        # Demo batch classification
        # await demo_batch_classification(orchestrator)

        # Demo performance metrics
        await demo_performance_metrics(orchestrator)

        print("\n" + "=" * 80)
        print("✨ 示範完成!")
        print("=" * 80)

    except Exception as e:
        print(f"\n❌ 示範執行失敗: {str(e)}")
        logger.error(f"Demo failed: {e}", exc_info=True)
        return 1

    return 0


if __name__ == "__main__":
    # Run the demonstration
    exit_code = asyncio.run(main())
    exit(exit_code)
