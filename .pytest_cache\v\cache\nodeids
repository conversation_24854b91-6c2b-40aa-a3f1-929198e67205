["tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_classification_error_fallback", "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_classification_with_intent_detection_disabled", "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_concurrent_batch_processing", "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_performance_metrics_collection", "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_successful_classification_workflow", "tests/test_intent_detection/test_domain_entities.py::TestDetectedIntent::test_create_custom_intent", "tests/test_intent_detection/test_domain_entities.py::TestDetectedIntent::test_create_predefined_intent", "tests/test_intent_detection/test_domain_entities.py::TestIntentDetectionRequest::test_create_request_without_metadata", "tests/test_intent_detection/test_domain_entities.py::TestIntentDetectionRequest::test_create_valid_request", "tests/test_intent_detection/test_domain_entities.py::TestIntentDetectionResult::test_create_error_result", "tests/test_intent_detection/test_domain_entities.py::TestIntentDetectionResult::test_create_successful_result", "tests/test_intent_detection/test_domain_entities.py::TestIntentDetectionResult::test_multi_intent_result", "tests/test_rag_entities.py::TestRAGCandidate::test_confidence_levels", "tests/test_rag_entities.py::TestRAGCandidate::test_create_rag_candidate_with_required_fields", "tests/test_rag_entities.py::TestRAGCandidate::test_similarity_score_validation", "tests/test_rag_entities.py::TestRAGSearchResult::test_confidence_filtering_properties", "tests/test_rag_entities.py::TestRAGSearchResult::test_create_search_result", "tests/test_rag_entities.py::TestRAGSearchResult::test_fallback_threshold_behavior", "tests/test_rag_integration.py::TestRAGIntegration::test_end_to_end_similarity_search", "tests/test_rag_integration.py::TestRAGIntegration::test_error_handling_in_integration", "tests/test_rag_integration.py::TestRAGIntegration::test_fallback_threshold_behavior", "tests/test_rag_integration.py::TestRAGIntegration::test_high_confidence_search_method", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_error_propagation", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_high_confidence_categories", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_high_confidence_via_parameters", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_similar_categories_with_defaults", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_similar_categories_with_overrides", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_with_expanded_results", "tests/test_similarity_search_use_case.py::TestSimilaritySearchUseCase::test_search_with_expanded_results_limit"]