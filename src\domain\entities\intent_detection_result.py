from dataclasses import dataclass
from typing import List, Optional
from .detected_intent import DetectedIntent


@dataclass
class IntentDetectionResult:
    """
    Complete result of intent detection and content summarization analysis.
    
    Contains all detected intents, generated content summary,
    performance metrics, and any error information.
    """
    
    complaint_id: str
    detected_intents: List[DetectedIntent]
    content_summary: str  # Generated summary for vector embedding
    processing_time_ms: int
    error_message: Optional[str] = None