from abc import ABC, abstractmethod
from ..entities.rag_search import RAGSearchResult


class RAGRetrieverInterface(ABC):
    """Interface for semantic similarity search in complaint categories."""
    
    @abstractmethod
    async def search_similar_categories(
        self,
        query_text: str,
        similarity_threshold: float = 0.75,
        fallback_threshold: float = 0.6,
        max_results: int = 5
    ) -> RAGSearchResult:
        """
        Search for categories similar to the given query text.
        
        Args:
            query_text: Complaint text to find similar categories for
            similarity_threshold: Primary cosine similarity threshold (0.75)
            fallback_threshold: Fallback threshold when primary fails (0.6)
            max_results: Maximum candidates to return per index
            
        Returns:
            RAGSearchResult with ranked candidates and metadata
            
        Raises:
            ValueError: If query_text is empty or invalid thresholds
            ElasticsearchConnectionError: If Elasticsearch is unavailable
        """
        pass