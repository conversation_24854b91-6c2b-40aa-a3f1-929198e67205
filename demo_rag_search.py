#!/usr/bin/env python3
"""
Demo script for RAG Similarity Search functionality.

This script demonstrates how to use the new RAG similarity search feature
to find similar complaint categories based on semantic similarity.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config import Settings
from src.infrastructure.embedders.gemini_embedder import GeminiEmbedder
from src.infrastructure.rag_elasticsearch_retriever import RAGElasticsearchRetriever


async def demo_rag_search():
    """Demonstrate RAG similarity search functionality."""

    print("🚀 RAG Similarity Search Demo")
    print("=" * 50)

    # Load settings (requires .env file with GEMINI_API_KEY)
    try:
        settings = Settings()
        print("✅ Settings loaded")
        print(f"   - Elasticsearch URL: {settings.elasticsearch_url}")
        print(
            f"   - Primary Threshold: {settings.rag_search.primary_similarity_threshold}"
        )
        print(
            f"   - Fallback Threshold: {settings.rag_search.fallback_similarity_threshold}"
        )
    except Exception as e:
        print(f"❌ Failed to load settings: {e}")
        print("   Please ensure .env file exists with GEMINI_API_KEY")
        return

    print("\n📦 Creating components...")

    # Create embedder
    try:
        embedder = GeminiEmbedder(settings)
        print("✅ GeminiEmbedder created")
    except Exception as e:
        print(f"❌ Failed to create embedder: {e}")
        return

    # Create RAG retriever
    try:
        rag_retriever = RAGElasticsearchRetriever(settings, embedder)
        print("✅ RAGElasticsearchRetriever created")
    except Exception as e:
        print(f"❌ Failed to create RAG retriever: {e}")
        return

    # Create use case
    try:
        similarity_search = SimilaritySearchUseCase(rag_retriever, settings)
        print("✅ SimilaritySearchUseCase created")
    except Exception as e:
        print(f"❌ Failed to create use case: {e}")
        return

    print("\n🔍 Testing similarity search...")

    # Test queries
    test_queries = [
        "反映楊梅市老莊路往環東路的交通問題，尤其是在上下班尖峰時段。此路段常出現嚴重壅塞，從校前路至楊梅市區的路段(環東路口)，由於流量過低，無法有效分散五楊高速的進入車流，導致短短300米的距離需耗時超過10分鐘，且交通事故頻繁發生。\r\n\r\n為改善該路段交通狀況，我懇請市府短期內調配人力，於環東路口進行車流調控。同時，也建議市府長期規劃相關道路調整方案，從根本上解決此處的交通壅塞問題，進而提升市民出行的便利性與安全性。\r\n\r\n謹此陳情，懇請市府重視並妥善處理，造福市民。",
        "你好，請問停車場能夠收費但不開發票嗎？\r\n前些天至桃園市中壢區中山路的 中山停車場 停車，到自動繳費機付費時，竟然沒有開立統一發票！\r\n請相關單位查核此停車場 是否有合法登記？\r\n是否有繳交相關營業稅金？\r\n沒開發票的停車場等於是逃漏稅吧！？\r\n中山停車場：\r\nhttps://www.google.com/maps/place/Zhongshan+Parking/@24.9553092,121.2274117,3a,75y,153.87h,90t/data=!3m7!1e1!3m5!1s1Cl-x8LpW6CvBm2sRrLi3w!2e0!6shttps:%2F%2Fstreetviewpixels-pa.googleapis.com%2Fv1%2Fthumbnail%3Fpanoid%3D1Cl-x8LpW6CvBm2sRrLi3w%26cb_client%3Dsearch.gws-prod.gps%26w%3D211%26h%3D120%26yaw%3D153.86594%26pitch%3D0%26thumbfov%3D100!7i16384!8i8192!4m14!1m7!3m6!1s0x34682352f26201a9:0xd0f874756b0e7970!2sZhongshan+Parking!8m2!3d24.9552535!4d121.2274501!16s%2Fg%2F11svzmw5jj!3m5!1s0x34682352f26201a9:0xd0f874756b0e7970!8m2!3d24.9552535!4d121.2274501!16s%2Fg%2F11svzmw5jj?coh=205409&entry=ttu&g_ep=EgoyMDI0MDkxOC4xIKXMDSoASAFQAw%3D%3D",
        "非常感謝政府單位照顧到勞工所需，提供上課實習機會，以讓廣大百姓增長所長，但上課路途發現一些不便之處，多數同學也有發現此問題～\r\n而本人因居住地區較其他縣市同學至職訓中心上課較近，故而選擇通勤。上課時間需在8:20前到達職訓上課，故7:20到8:00這段期間無車可搭。做為學生一定會希望自己最晚能在8:15前到，畢竟要爬個小山打卡，需要緩衝時間。\r\n而距離時間點最近的兩班車次251與新竹5641，都會超過八點到站，就算搭車到校一定是遲到，想請問是否與此期間增加到站班次，再麻煩了！🙏🙏🙏",
        "今天中午我媽媽於南門市場約12:30左右站欲搭乘225A公車回家，在上車時才將雨傘拐杖撐到車門階梯欲上車時突然間車門即關閉並開走，還好我八十一歲的母親機警放掉拐杖，否則就被公車拖走，你們這是謀殺吧！我媽媽事後就坐計程車追公車找到拐杖，我有拐杖受損照片佐證可以佐證司機的疏忽與惡劣行徑，呈請市長以慈悲胸懷多多體諒這些年長父母們行動不便，要求這些司機不應該以績效為主而是要以民眾安全為主，白天上班時間要搭乘的都是老弱婦孺居多，如果司機素質如此，則是民安全真的堪慮，希望我反應後市長大人能正視這問題不要等到發生意外了才要道歉，市民不要道歉，而是我們反映了要正視問題徹底解決減少意外的悲劇，最後謝謝市長百忙撥冗看信，還請市長多多幫忙這些老人家！誠心希望市長提供給大家的是一輛平安到家的公車而不是奪命公車謝謝！（附上今天被車門夾著跑的雨傘拐杖慘況照片，這如果夾的是我媽媽的腳，應該是一條人命了吧！）",
        "你好，附件檔是上一封本人提出對於凌雲國中增置捷運站的相關需求，其實在中原路一段有2間學校，一所為凌雲國中，一所為方曙商工，如此鄰近的2所學校，但卻沒有捷運站的設置於此，實在是不合理，比如新北市捷運中蘆線，就有5站是設置於學校附近，但是中原路一段明明就有十分鄰近的2所學校，但卻尚未列入捷運規劃內的增設一站，十分令人費夷所思，「本府將於交通部111年5月6日核備之第二階段捷運路網路線架構下，持續檢討目標年人口成長的運量需求及地域環境等因素，並依運量需求及地區環境條件整體規劃考量，以確保本計畫推動可行」這段文字是貴單位於上次的回函內容，但中原路一段此處真的是非常需要捷運站的設置，是否可將此路段新增至可行性研究的評估，也能提昇龍潭此處的交通便利性.",
        "後站計程車司機在排班時，時常開著門站在車邊抽菸，也有坐在車上抽菸，乘客上車才把菸熄掉。我是通勤族，每天從火車站出來搭計程車回家，全身都是煙味，搭計程車就像處在吸菸室一樣。\r\n請提醒排班司機菸害防制法的規範，計程車應是禁菸場所，也希望提醒司機保持車內整潔...",
        "您好，我想請問5個月大的嬰兒，是否可以由爸媽以揹巾抱在身上來搭乘計程車？或者一定得使用安全座椅呢？謝謝。",
        "平鎮區南東路104巷口，多次被車子撞歪！想麻煩改善方案。不然裡面住戶無法看道路的來車！很容易會發生交通事故。",
        "您好：\r\n我是一名在職工作者。今年六月，我無意中得知某大學推廣的「烘焙丙級證照學分班」，該班主打政府補助，配合桃園市政府就業職訓服務處的「113年桃園市青年暨中高齡者自主學習方案」。在這個方案的幫助下，原本兩萬多的學費，我只需自行負擔三千多。在經過瞭解後我得知，這個方案要求學生先支付全額學費，並且只有在所有課程結束、取得學分證明後的隔天，才能申請補助。\r\n我感到十分錯愕與不解。既然我在六月時已經報名並參加了政府補助班，正常情況下應該會認為自己能夠獲得補助，沒想到經費會如此迅速耗盡。且在這過程中，經費的剩餘情況未曾向民眾公開，讓我們無從預知補助是否仍可申請，而補助的突然停止，則讓人措手不及。對於那些在8月3日便打算申請的人來說，他們的錯愕程度更是可想而知。我原以為只要完成課程，便能申請到政府的補助金，減輕沉重的生活壓力。\r\n然而，當我於8月12日，也就是完成所有課程的隔天，準備申請補助時，我在網站上竟看到「預算經費已用完，停止補助」的通知。這對我而言猶如晴天霹靂，腦海一片空白，甚至覺得自己遭遇了詐騙。在冷靜下來後，我發現這個通知是8月2日才發布的，公告立即生效，並且完全沒有提供任何緩衝期。原本補助計劃的截止時間是113年12月15日，但因經費用罄，竟在毫無預警的情況下突然停止受理。\r\n隨後，我打電話向負責這個方案的單位進行確認，卻意外發現該方案只要是在取得學分證明的翌日起90日內，皆可申請補助。並且，負責人告知，只要是在90日內，今年取得的學分，也能向明年的方案申請補助。這讓我感到極其不合理。這意味著，今年十月才取得學分的人，依然能申請補助，而我在八月初便取得學分，卻無法申請。這樣的安排顯然不公平，尤其是對於在八月、九月取得學分的學生來說，這樣的制度讓我們成為唯一無法申請補助的一群人。\r\n因此，我懇請能夠重新審視這個方案的執行方式。我建議明年的經費應優先給今年8月2日公告經費截止後90日內取得學分的人申請，而不是給明年方案公告前90日內取得學分的人。這樣的調整將會更符合情理，也能更好地保障我們這些已經付出努力並且滿懷希望的學生。\r\n\r\n感謝您的聆聽，期盼能獲得您的關注與協助。",
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test Query {i} ---")
        print(f"Query: {query}")

        try:
            # Perform similarity search
            result = await similarity_search.search_similar_categories(query)

            # Display results
            print("📊 Search Results:")
            print(f"   - Total candidates: {len(result.candidates)}")
            print(f"   - Used fallback: {result.used_fallback_threshold}")
            print(
                f"   - High confidence matches: {len(result.high_confidence_candidates)}"
            )

            # Show top 3 candidates
            print("   - Top 3 candidates:")
            for j, candidate in enumerate(result.candidates[:3], 1):
                print(
                    f"   {j}. {candidate.category_type} ({candidate.similarity_score:.3f})"
                )
                print(f"      Source: {candidate.index_source}")
                print(f"      Confidence: {candidate.confidence_level}")

            # Show metadata
            metadata = result.search_metadata
            print("⏱️  Performance:")
            print(f"   - Total time: {metadata.total_search_time_ms:.2f}ms")
            print(f"   - Embedding time: {metadata.embedding_generation_time_ms:.2f}ms")
            print(f"   - Search time: {metadata.elasticsearch_query_time_ms:.2f}ms")

        except Exception as e:
            print(f"❌ Search failed: {e}")
            continue

    # Test high-confidence search (using equal thresholds)
    # print("\n--- High-Confidence Search (Equal Thresholds) ---")
    # print("Query: 交通號誌損壞問題")
    # try:
    #     result = await similarity_search.search_similar_categories(
    #         query_text="交通號誌損壞問題",
    #         similarity_threshold=0.75,
    #         fallback_threshold=0.75,  # Same threshold = no fallback
    #     )
    #     print(f"Total candidates: {len(result.candidates)}")
    #     print(f"Used fallback: {result.used_fallback_threshold}")
    #     print("Top 3 candidates:")
    #     for candidate in result.candidates[:3]:
    #         print(f"  - {candidate.category_type}: {candidate.similarity_score:.3f}")
    # except Exception as e:
    #     print(f"❌ High-confidence search failed: {e}")

    # # Cleanup
    # try:
    #     await rag_retriever.close()
    #     print("\n✅ Resources cleaned up")
    # except Exception as e:
    #     print(f"⚠️  Cleanup warning: {e}")

    print("\n🎉 Demo completed!")


def main():
    """Main entry point."""
    print("Starting RAG Similarity Search Demo...")
    try:
        asyncio.run(demo_rag_search())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"💥 Demo failed: {e}")


if __name__ == "__main__":
    main()
