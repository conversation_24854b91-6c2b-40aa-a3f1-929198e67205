"""Test SimilaritySearchUseCase."""

import pytest
from unittest.mock import AsyncMock, Mock

from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.domain.interfaces.rag_retriever import RAGRetrieverInterface
from src.domain.entities.rag_search import RA<PERSON>earchR<PERSON>ult, RAGSearchMetadata
from src.domain.entities.rag_candidate import RAGCandidate
from src.config.settings import Settings, RAGSearchSettings


@pytest.fixture
def mock_settings():
    """Create mock settings."""
    settings = Mock(spec=Settings)
    settings.rag_search = RAGSearchSettings(
        primary_similarity_threshold=0.75,
        fallback_similarity_threshold=0.6,
        max_candidates_per_index=5
    )
    return settings


@pytest.fixture
def mock_rag_retriever():
    """Create mock RAG retriever."""
    return AsyncMock(spec=RAGRetrieverInterface)


@pytest.fixture
def sample_search_result():
    """Create sample search result."""
    metadata = RAGSearchMetadata(
        embedding_generation_time_ms=100.0,
        elasticsearch_query_time_ms=200.0,
        total_search_time_ms=300.0,
        primary_threshold_used=0.75,
        total_candidates_found=2,
        sub_categories_searched=1,
        main_categories_searched=1
    )
    
    candidates = [
        RAGCandidate(
            category_id="traffic_signs",
            similarity_score=0.85,
            category_data={"main_category": "交通設施", "sub_category": "交通號誌"},
            index_source="sub_categories",
            category_type="交通號誌",
            combined_text="交通號誌相關問題"
        ),
        RAGCandidate(
            category_id="road_maintenance", 
            similarity_score=0.78,
            category_data={"main_category": "道路維護"},
            index_source="main_categories",
            category_type="道路維護",
            combined_text="道路維護相關問題"
        )
    ]
    
    return RAGSearchResult(
        candidates=candidates,
        used_fallback_threshold=False,
        search_metadata=metadata
    )


class TestSimilaritySearchUseCase:
    """Test SimilaritySearchUseCase."""
    
    @pytest.mark.asyncio
    async def test_search_similar_categories_with_defaults(
        self, 
        mock_rag_retriever, 
        mock_settings, 
        sample_search_result
    ):
        """Test search with default parameters."""
        # Setup
        mock_rag_retriever.search_similar_categories.return_value = sample_search_result
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute
        result = await use_case.search_similar_categories("道路標誌損壞")
        
        # Verify
        assert result == sample_search_result
        assert len(result.candidates) == 2
        
        # Verify retriever was called with correct defaults
        mock_rag_retriever.search_similar_categories.assert_called_once_with(
            query_text="道路標誌損壞",
            similarity_threshold=0.75,
            fallback_threshold=0.6,
            max_results=5
        )
    
    @pytest.mark.asyncio
    async def test_search_similar_categories_with_overrides(
        self, 
        mock_rag_retriever, 
        mock_settings, 
        sample_search_result
    ):
        """Test search with parameter overrides."""
        # Setup
        mock_rag_retriever.search_similar_categories.return_value = sample_search_result
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute with overrides
        result = await use_case.search_similar_categories(
            query_text="交通號誌問題",
            similarity_threshold=0.8,
            fallback_threshold=0.65,
            max_results=10
        )
        
        # Verify
        assert result == sample_search_result
        
        # Verify retriever was called with overridden values
        mock_rag_retriever.search_similar_categories.assert_called_once_with(
            query_text="交通號誌問題",
            similarity_threshold=0.8,
            fallback_threshold=0.65,
            max_results=10
        )
    
    @pytest.mark.asyncio
    async def test_search_high_confidence_via_parameters(
        self, 
        mock_rag_retriever, 
        mock_settings, 
        sample_search_result
    ):
        """Test high-confidence search by setting equal thresholds."""
        # Setup
        mock_rag_retriever.search_similar_categories.return_value = sample_search_result
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute with same threshold for both parameters (no fallback)
        result = await use_case.search_similar_categories(
            query_text="道路維護問題",
            similarity_threshold=0.75,
            fallback_threshold=0.75  # Same as primary = no fallback
        )
        
        # Verify
        assert result == sample_search_result
        
        # Verify retriever was called with strict threshold (no fallback)
        mock_rag_retriever.search_similar_categories.assert_called_once_with(
            query_text="道路維護問題",
            similarity_threshold=0.75,
            fallback_threshold=0.75,  # Same as primary (no fallback)
            max_results=5
        )
    
    @pytest.mark.asyncio 
    async def test_search_with_expanded_results(
        self, 
        mock_rag_retriever, 
        mock_settings, 
        sample_search_result
    ):
        """Test expanded results search method."""
        # Setup
        mock_rag_retriever.search_similar_categories.return_value = sample_search_result
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute
        result = await use_case.search_with_expanded_results("環境問題", max_results=15)
        
        # Verify
        assert result == sample_search_result
        
        # Verify retriever was called with expanded max_results
        mock_rag_retriever.search_similar_categories.assert_called_once_with(
            query_text="環境問題",
            similarity_threshold=0.75,
            fallback_threshold=0.6, 
            max_results=15
        )
    
    @pytest.mark.asyncio
    async def test_search_with_expanded_results_limit(
        self, 
        mock_rag_retriever, 
        mock_settings, 
        sample_search_result
    ):
        """Test expanded results with system limit enforcement."""
        # Setup
        mock_rag_retriever.search_similar_categories.return_value = sample_search_result
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute with excessive max_results 
        result = await use_case.search_with_expanded_results("環境問題", max_results=30)
        
        # Verify system limit is enforced (capped at 20)
        mock_rag_retriever.search_similar_categories.assert_called_once_with(
            query_text="環境問題",
            similarity_threshold=0.75,
            fallback_threshold=0.6,
            max_results=20  # Capped at 20
        )
    
    @pytest.mark.asyncio
    async def test_error_propagation(
        self, 
        mock_rag_retriever, 
        mock_settings
    ):
        """Test that errors from retriever are propagated."""
        # Setup
        mock_rag_retriever.search_similar_categories.side_effect = ValueError("Test error")
        use_case = SimilaritySearchUseCase(mock_rag_retriever, mock_settings)
        
        # Execute and verify error is propagated
        with pytest.raises(ValueError, match="Test error"):
            await use_case.search_similar_categories("test query")