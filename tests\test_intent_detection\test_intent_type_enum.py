"""Unit tests for IntentType enum functionality."""

import pytest
from src.domain.entities.intent_type import IntentType


class TestIntentTypeEnum:
    """Test IntentType enum functionality."""
    
    def test_all_predefined_intents_exist(self):
        """Test that all predefined Taiwan government intents are available."""
        expected_intents = [
            "檢舉告發",
            "請求協助", 
            "抱怨問題",
            "請求改善",
            "諮詢問題",
            "不滿服務態度",
            "不滿人員專業度", 
            "感謝讚美",
            "提出建議",
            "其他"
        ]
        
        all_intents = IntentType.get_all_intents()
        
        for expected in expected_intents:
            assert expected in all_intents

    def test_get_all_intents_returns_list(self):
        """Test that get_all_intents returns a list."""
        intents = IntentType.get_all_intents()
        
        assert isinstance(intents, list)
        assert len(intents) >= 10  # Should have at least 10 predefined intents

    def test_is_valid_intent_with_predefined_intents(self):
        """Test intent validation with predefined intents."""
        valid_intents = [
            "檢舉告發",
            "請求協助",
            "抱怨問題"
        ]
        
        for intent in valid_intents:
            assert IntentType.is_valid_intent(intent) is True

    def test_is_valid_intent_with_custom_intents(self):
        """Test intent validation with custom intents (should return False)."""
        custom_intents = [
            "自定義意圖",
            "申請相關諮詢", 
            "特殊需求",
            ""
        ]
        
        for intent in custom_intents:
            assert IntentType.is_valid_intent(intent) is False

    def test_intent_type_enum_values(self):
        """Test that IntentType enum values match expected strings."""
        assert IntentType.REPORT_VIOLATION.value == "檢舉告發"
        assert IntentType.REQUEST_ASSISTANCE.value == "請求協助"
        assert IntentType.COMPLAINT_PROBLEM.value == "抱怨問題"
        assert IntentType.REQUEST_IMPROVEMENT.value == "請求改善"
        assert IntentType.CONSULTATION_INQUIRY.value == "諮詢問題"
        assert IntentType.DISSATISFIED_SERVICE_ATTITUDE.value == "不滿服務態度"
        assert IntentType.DISSATISFIED_STAFF_PROFESSIONALISM.value == "不滿人員專業度"
        assert IntentType.GRATITUDE_PRAISE.value == "感謝讚美"
        assert IntentType.SUGGEST_PROPOSAL.value == "提出建議"
        assert IntentType.OTHER.value == "其他"

    def test_case_sensitivity(self):
        """Test that intent validation is case sensitive."""
        # Exact match should work
        assert IntentType.is_valid_intent("檢舉告發") is True
        
        # Different case or characters should not work
        assert IntentType.is_valid_intent("检举告发") is False  # Simplified Chinese
        assert IntentType.is_valid_intent("檢舉告發 ") is False  # Extra space

    def test_enum_iteration(self):
        """Test iterating through all IntentType values."""
        all_values = [intent.value for intent in IntentType]
        expected_count = 10  # Based on current predefined intents
        
        assert len(all_values) == expected_count
        assert "檢舉告發" in all_values
        assert "其他" in all_values