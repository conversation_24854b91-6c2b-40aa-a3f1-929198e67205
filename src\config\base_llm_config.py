"""Base configuration classes for the application."""

from pydantic import BaseModel, Field


class BaseConfig(BaseModel):
    """Base configuration class with common settings."""

    class Config:
        """Pydantic configuration."""

        extra = "forbid"  # Prevent extra fields
        validate_assignment = True  # Validate on assignment


class PerformanceConfig(BaseConfig):
    """Common performance configuration."""

    max_concurrent_requests: int = Field(default=10, ge=1, le=50)
    request_timeout_seconds: int = Field(default=30, ge=10, le=120)
    enable_retries: bool = Field(default=True)


class SimilarityConfig(BaseConfig):
    """Configuration for similarity thresholds."""

    primary_similarity_threshold: float = Field(default=0.75, ge=0.0, le=1.0)
    fallback_similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0)

    def model_post_init(self, __context) -> None:
        """Validate that fallback threshold is lower than primary threshold."""
        if self.fallback_similarity_threshold >= self.primary_similarity_threshold:
            raise ValueError("Fallback threshold must be lower than primary threshold")
