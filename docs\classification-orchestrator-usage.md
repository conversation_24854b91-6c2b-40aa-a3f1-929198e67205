# Classification Logic Orchestrator - Usage Guide

## Overview

The Classification Logic Orchestrator is the main component that coordinates the complete complaint classification workflow. It integrates intent detection, RAG similarity search, and LLM-based classification to provide accurate categorization of Taiwan government complaints.

## Architecture

```
ComplaintInput → Intent Detection → RAG Search → LLM Classification → ClassificationResult
                      ↓                ↓              ↓
                 Detected Intents  Similar Categories  Final Decision
                   + Summary       + Similarity Scores  + Confidence
```

## Key Features

- **Multi-LLM Support**: OpenAI GPT-4o, Anthropic Claude Sonnet-4, Google Gemini 2.5-flash
- **Feature Flags**: Configurable intent detection and summary generation
- **Batch Processing**: Concurrent processing with configurable limits
- **Error Resilience**: Automatic fallback to default categories
- **Performance Monitoring**: Comprehensive metrics collection

## Quick Start

### 1. Environment Setup

Create a `.env` file with required API keys:

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key

# Optional (based on LLM provider choice)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Elasticsearch
ELASTICSEARCH_URL=http://localhost:9200
```

### 2. Basic Usage

```python
from src.config.settings import Settings
from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.domain.entities.complaint_input import ComplaintInput

# Load settings
settings = Settings()

# Create orchestrator
orchestrator = await create_orchestrator(settings)

# Classify a single complaint
complaint = ComplaintInput(
    case_id="TEST-001",
    subject="交通號誌故障",
    content="紅綠燈故障，請儘速修復"
)

result = await orchestrator.execute(complaint)
print(f"Category: {result.main_category} > {result.sub_category}")
print(f"Confidence: {result.confidence}")
```

### 3. Batch Processing

```python
# Process multiple complaints concurrently
complaints = [complaint1, complaint2, complaint3]
results = await orchestrator.execute_batch(
    complaints, 
    concurrency_limit=5
)

for result in results:
    print(f"{result.case_id}: {result.sub_category} ({result.confidence})")
```

## Configuration

### LLM Provider Settings

Configure which LLM provider to use in your settings:

```python
# In your .env or settings
CLASSIFICATION_LLM_PROVIDER=google  # Options: openai, anthropic, google
```

### Feature Flags

Control optional features:

```python
settings.classification.enable_llm_analysis = True  # Enable LLM analysis (intent detection + summarization)
```

### Performance Tuning

```python
settings.classification.max_concurrent_classifications = 10  # Batch concurrency
settings.classification.classification_timeout_seconds = 30  # LLM timeout
settings.classification.primary_similarity_threshold = 0.75  # RAG threshold
```

## Demo Scripts

### Complete Workflow Demo

```bash
uv run demo_classification_orchestrator.py
```

This demonstrates:
- Single complaint classification
- Batch processing
- Performance metrics
- Error handling

### Main Application

```bash
uv run src/main.py
```

Shows the complete system including data processing checks.

## API Reference

### ClassifyComplaintsUseCase

#### Methods

**`execute(complaint: ComplaintInput) -> ClassificationResult`**
- Classifies a single complaint through the complete workflow
- Returns detailed classification result with confidence and reasoning

**`execute_batch(complaints: List[ComplaintInput], concurrency_limit: int = 10) -> List[ClassificationResult]`**
- Processes multiple complaints concurrently
- Maintains order of results matching input order

**`get_performance_metrics() -> dict`**
- Returns comprehensive performance statistics
- Includes success rates, timing metrics, and confidence distributions

### Configuration Classes

**`ClassificationConfig`**
- LLM provider settings
- API keys configuration  
- Performance parameters
- Similarity thresholds

**`ClassificationSettings`** 
- Feature flags for optional components
- Concurrency and timeout settings
- Provider-specific model mappings

## Testing

### Unit Tests

```bash
uv run pytest tests/test_classification_workflow.py -v
```

### Integration Tests

The test suite includes:
- End-to-end workflow testing
- Error handling validation
- Batch processing verification
- Performance metrics collection
- Mock LLM responses

## Performance Optimization

### Concurrency Settings

- **Batch Processing**: Use concurrency_limit=5-10 for optimal throughput
- **API Rate Limits**: Respect provider rate limits (60 requests/minute for most)
- **Memory Usage**: Monitor memory with large batches

### Caching

- RAG similarity results are not cached by default
- Intent detection results could be cached for repeated content
- LLM responses should not be cached due to variability

### Monitoring

```python
# Get real-time performance metrics
metrics = orchestrator.get_performance_metrics()
print(f"Success rate: {metrics['success_rate']*100:.1f}%")
print(f"Average processing time: {metrics['avg_total_time_ms']:.0f}ms")
```

## Error Handling

The orchestrator implements comprehensive error handling:

1. **Component Failures**: If intent detection fails, continues with original content
2. **RAG Search Issues**: Falls back to empty results with default classification
3. **LLM Failures**: Returns "其它" category with detailed error reasoning
4. **Timeout Handling**: Configurable timeouts with graceful degradation

### Common Issues

**"API key not provided"**
- Check your .env file has the correct API key for your chosen LLM provider

**"Elasticsearch connection failed"**  
- Verify Elasticsearch is running and accessible
- Check ELASTICSEARCH_URL in your configuration

**"Classification timeout"**
- Increase classification_timeout_seconds in settings
- Check network connectivity to LLM providers

## Best Practices

1. **Provider Selection**: Google Gemini offers good balance of speed and accuracy
2. **Batch Size**: Process 10-20 complaints per batch for optimal performance
3. **Error Monitoring**: Monitor success rates and adjust thresholds accordingly
4. **Feature Flags**: Start with both intent detection and summary enabled
5. **Timeout Settings**: Set realistic timeouts (30-60 seconds) for production use

## Advanced Usage

### Custom LLM Provider

Extend the `AgnoLLMClassificationService` to add support for additional LLM providers:

```python
# Add to ClassificationConfig.llm_model_mapping
"custom_provider": "custom-model-id"

# Extend _initialize_agent method
elif self.config.llm_provider.lower() == "custom":
    model = CustomModel(id="custom-model-id", api_key=api_key)
```

### Custom Prompt Engineering

Override the classification instructions:

```python
def _build_classification_instructions(self) -> str:
    return """
    Your custom classification instructions here...
    """
```

### Integration with External Systems

```python
# Example: Integration with ticket system
from src.domain.entities.confidence_level import ConfidenceLevel

async def process_and_create_tickets(complaints):
    results = await orchestrator.execute_batch(complaints)
    
    for result in results:
        ticket_id = await ticket_system.create_ticket(
            category=result.main_category,
            priority="high" if result.confidence == ConfidenceLevel.HIGH else "normal"
        )
        print(f"Created ticket {ticket_id} for {result.case_id}")
```

## Support

For issues or questions:

1. Check the logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test with a single complaint first before batch processing
4. Monitor performance metrics for optimization opportunities