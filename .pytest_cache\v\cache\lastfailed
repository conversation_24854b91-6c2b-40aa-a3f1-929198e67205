{"tests/test_rag_integration.py::TestRAGIntegration::test_end_to_end_similarity_search": true, "tests/test_rag_integration.py::TestRAGIntegration::test_fallback_threshold_behavior": true, "tests/test_rag_integration.py::TestRAGIntegration::test_high_confidence_search_method": true, "tests/test_rag_integration.py::TestRAGIntegration::test_error_handling_in_integration": true, "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_successful_classification_workflow": true, "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_classification_with_intent_detection_disabled": true, "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_classification_error_fallback": true, "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_concurrent_batch_processing": true, "tests/test_classification_workflow.py::TestClassificationWorkflowIntegration::test_performance_metrics_collection": true}