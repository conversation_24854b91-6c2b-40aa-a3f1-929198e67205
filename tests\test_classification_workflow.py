"""Integration tests for the complete classification workflow."""

import asyncio
import logging
from unittest.mock import AsyncMock, <PERSON><PERSON>, patch
from datetime import datetime

import pytest

from src.application.use_cases.classify_complaints import ClassifyComplaintsUseCase
from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config.settings import Settings
from src.domain.entities.complaint_input import ComplaintInput
from src.domain.entities.classification_result import ClassificationResult
from src.domain.entities.confidence_level import ConfidenceLevel
from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.intent_detection_result import IntentDetectionResult
from src.domain.entities.intent_type import IntentType
from src.domain.entities.detected_intent import DetectedIntent
from src.domain.entities.rag_search import RAGSearchResult
from src.domain.entities.rag_candidate import RAGCandidate
from src.infrastructure.llm_classification.agno_llm_classifier import ClassificationConfig

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@pytest.fixture
def sample_complaint():
    """Sample complaint input for testing."""
    return ComplaintInput(
        case_id="TEST-001",
        subject="交通號誌故障",
        content="台北市信義區基隆路上的紅綠燈故障了，造成交通混亂，請儘速派人維修。這個路口經常有這個問題，希望能徹底解決。"
    )


@pytest.fixture
def sample_intent_result():
    """Sample intent detection result."""
    return IntentDetectionResult(
        complaint_id="TEST-001",
        detected_intents=[
            DetectedIntent(
                intent_name="交通設施維修",
                confidence_score=0.85,
                reasoning="陳情內容明確提到紅綠燈故障需要維修"
            )
        ],
        content_summary="信義區基隆路紅綠燈故障，造成交通問題，需要維修處理",
        processing_time_ms=150
    )


@pytest.fixture  
def sample_rag_result():
    """Sample RAG search result."""
    return RAGSearchResult(
        candidates=[
            RAGCandidate(
                main_category="交通號誌、標誌、標線及大眾運輸",
                sub_category="交通標誌牌面、反射鏡損壞傾斜", 
                description="交通號誌設備故障、損壞的維修處理",
                similarity_score=0.82,
                keywords=["號誌", "故障", "維修", "交通"]
            ),
            RAGCandidate(
                main_category="交通號誌、標誌、標線及大眾運輸",
                sub_category="號誌故障停電",
                description="交通號誌因故障或停電無法正常運作",
                similarity_score=0.78,
                keywords=["號誌", "故障", "停電"]
            )
        ],
        used_fallback_threshold=False,
        search_metadata=Mock(total_search_time_ms=45.2)
    )


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    with patch.object(Settings, '__init__', lambda x: None):
        settings = Settings()
        settings.gemini_api_key = "test-gemini-key"
        settings.openai_api_key = "test-openai-key"
        settings.anthropic_api_key = "test-anthropic-key"
        settings.classification = Mock()
        settings.classification.enable_llm_analysis = True
        settings.classification.llm_provider = "google"
        settings.classification.llm_temperature = 0.1
        settings.classification.llm_max_tokens = 500
        settings.classification.primary_similarity_threshold = 0.75
        settings.classification.fallback_similarity_threshold = 0.6
        settings.classification.max_concurrent_classifications = 10
        settings.classification.classification_timeout_seconds = 30
        return settings


@pytest.fixture
def mock_detect_intents_use_case():
    """Mock intent detection use case."""
    mock_use_case = AsyncMock(spec=DetectIntentsUseCase)
    return mock_use_case


@pytest.fixture
def mock_similarity_search_use_case():
    """Mock similarity search use case."""
    mock_use_case = AsyncMock(spec=SimilaritySearchUseCase)
    return mock_use_case


@pytest.fixture
def classification_config(mock_settings):
    """Classification config for testing."""
    return ClassificationConfig(
        llm_provider="google",
        openai_api_key="test-openai-key",
        anthropic_api_key="test-anthropic-key", 
        google_api_key="test-gemini-key",
        temperature=0.1,
        max_tokens=500,
        primary_similarity_threshold=0.75,
        fallback_similarity_threshold=0.6,
        classification_timeout_seconds=30,
        enable_retries=True
    )


class TestClassificationWorkflowIntegration:
    """Integration tests for complete classification workflow."""
    
    @pytest.mark.asyncio
    async def test_successful_classification_workflow(
        self,
        sample_complaint,
        sample_intent_result,
        sample_rag_result,
        mock_detect_intents_use_case,
        mock_similarity_search_use_case,
        classification_config
    ):
        """Test successful end-to-end classification workflow."""
        
        # Setup mocks
        mock_detect_intents_use_case.execute.return_value = sample_intent_result
        mock_similarity_search_use_case.search_similar_categories.return_value = sample_rag_result
        
        # Create orchestrator with mocked dependencies
        orchestrator = ClassifyComplaintsUseCase(
            detect_intents_use_case=mock_detect_intents_use_case,
            similarity_search_use_case=mock_similarity_search_use_case,
            classification_config=classification_config,
            enable_llm_analysis=True
        )
        
        # Mock the LLM classifier
        with patch.object(orchestrator._llm_classifier, 'classify_with_context') as mock_classify:
            mock_classify.return_value = ClassificationResult(
                case_id="TEST-001",
                main_category="交通號誌、標誌、標線及大眾運輸",
                sub_category="交通標誌牌面、反射鏡損壞傾斜",
                sub_description="交通號誌設備故障、損壞的維修處理",
                intents=[IntentType.INFRASTRUCTURE_REPAIR],
                confidence=ConfidenceLevel.HIGH,
                reasoning="陳情內容明確指出紅綠燈故障問題，與RAG搜尋結果高度匹配",
                rag_candidates=sample_rag_result.candidates,
                similarity_score=0.82,
                processing_time_ms=250
            )
            
            # Execute workflow
            result = await orchestrator.execute(sample_complaint)
            
            # Verify result
            assert result.case_id == "TEST-001"
            assert result.main_category == "交通號誌、標誌、標線及大眾運輸"
            assert result.sub_category == "交通標誌牌面、反射鏡損壞傾斜"
            assert result.confidence == ConfidenceLevel.HIGH
            assert result.similarity_score == 0.82
            assert len(result.intents) > 0
            
            # Verify use case calls
            mock_detect_intents_use_case.execute.assert_called_once()
            mock_similarity_search_use_case.search_similar_categories.assert_called_once()
            mock_classify.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_classification_with_intent_detection_disabled(
        self,
        sample_complaint,
        sample_rag_result,
        mock_detect_intents_use_case,
        mock_similarity_search_use_case,
        classification_config
    ):
        """Test workflow with intent detection disabled."""
        
        # Setup mocks
        mock_similarity_search_use_case.search_similar_categories.return_value = sample_rag_result
        
        # Create orchestrator with intent detection disabled
        orchestrator = ClassifyComplaintsUseCase(
            detect_intents_use_case=mock_detect_intents_use_case,
            similarity_search_use_case=mock_similarity_search_use_case,
            classification_config=classification_config,
            enable_llm_analysis=False
        )
        
        # Mock the LLM classifier
        with patch.object(orchestrator._llm_classifier, 'classify_with_context') as mock_classify:
            mock_classify.return_value = ClassificationResult(
                case_id="TEST-001",
                main_category="交通號誌、標誌、標線及大眾運輸",
                sub_category="其它",
                sub_description="無法確定具體子類別",
                intents=[],
                confidence=ConfidenceLevel.LOW,
                reasoning="未進行意圖檢測，僅基於RAG搜尋結果分類",
                rag_candidates=sample_rag_result.candidates,
                similarity_score=0.82,
                processing_time_ms=180
            )
            
            # Execute workflow
            result = await orchestrator.execute(sample_complaint)
            
            # Verify that intent detection was not called
            mock_detect_intents_use_case.execute.assert_not_called()
            
            # Verify RAG search was still called with original content
            mock_similarity_search_use_case.search_similar_categories.assert_called_once()
            
            # Verify result
            assert result.case_id == "TEST-001"
            assert result.intents == []
            assert result.confidence == ConfidenceLevel.LOW
    
    @pytest.mark.asyncio
    async def test_classification_error_fallback(
        self,
        sample_complaint,
        mock_detect_intents_use_case,
        mock_similarity_search_use_case,
        classification_config
    ):
        """Test error handling and fallback to default classification."""
        
        # Setup mock to raise exception
        mock_detect_intents_use_case.execute.side_effect = Exception("Intent detection failed")
        
        # Create orchestrator
        orchestrator = ClassifyComplaintsUseCase(
            detect_intents_use_case=mock_detect_intents_use_case,
            similarity_search_use_case=mock_similarity_search_use_case,
            classification_config=classification_config,
            enable_llm_analysis=True
        )
        
        # Execute workflow
        result = await orchestrator.execute(sample_complaint)
        
        # Verify fallback result
        assert result.case_id == "TEST-001"
        assert result.main_category == "其它"
        assert result.sub_category == "其它"
        assert result.confidence == ConfidenceLevel.LOW
        assert "工作流程執行失敗" in result.reasoning
        assert result.intents == []
        assert result.similarity_score == 0.0
    
    @pytest.mark.asyncio
    async def test_concurrent_batch_processing(
        self,
        mock_detect_intents_use_case,
        mock_similarity_search_use_case,
        classification_config
    ):
        """Test concurrent batch processing of multiple complaints."""
        
        # Create multiple test complaints
        complaints = [
            ComplaintInput(
                case_id=f"BATCH-{i:03d}",
                subject=f"測試陳情 {i}",
                content=f"這是第 {i} 個測試陳情內容，用於驗證批次處理功能。"
            )
            for i in range(1, 6)
        ]
        
        # Setup mocks
        mock_detect_intents_use_case.execute.return_value = IntentDetectionResult(
            complaint_id="BATCH-001",
            detected_intents=[],
            content_summary="測試摘要",
            processing_time_ms=100
        )
        
        mock_similarity_search_use_case.search_similar_categories.return_value = RAGSearchResult(
            candidates=[
                RAGCandidate(
                    main_category="其它",
                    sub_category="其它",
                    description="預設類別",
                    similarity_score=0.5,
                    keywords=["測試"]
                )
            ],
            used_fallback_threshold=False,
            search_metadata=Mock(total_search_time_ms=30.0)
        )
        
        # Create orchestrator
        orchestrator = ClassifyComplaintsUseCase(
            detect_intents_use_case=mock_detect_intents_use_case,
            similarity_search_use_case=mock_similarity_search_use_case,
            classification_config=classification_config,
            enable_llm_analysis=True
        )
        
        # Mock the LLM classifier for batch processing
        with patch.object(orchestrator._llm_classifier, 'classify_with_context') as mock_classify:
            mock_classify.side_effect = lambda **kwargs: ClassificationResult(
                case_id=kwargs.get('case_id', 'unknown'),
                main_category="其它",
                sub_category="其它",
                sub_description="批次處理測試",
                intents=[],
                confidence=ConfidenceLevel.LOW,
                reasoning="批次處理測試分類",
                rag_candidates=[],
                similarity_score=0.5,
                processing_time_ms=150
            )
            
            # Execute batch processing
            results = await orchestrator.execute_batch(
                complaints, 
                concurrency_limit=3
            )
            
            # Verify results
            assert len(results) == 5
            for i, result in enumerate(results, 1):
                assert result.case_id == f"BATCH-{i:03d}"
                assert result.main_category == "其它"
                assert result.confidence == ConfidenceLevel.LOW
            
            # Verify all complaints were processed
            assert mock_classify.call_count == 5
    
    @pytest.mark.asyncio
    async def test_performance_metrics_collection(
        self,
        sample_complaint,
        sample_intent_result,
        sample_rag_result,
        mock_detect_intents_use_case,
        mock_similarity_search_use_case,
        classification_config
    ):
        """Test performance metrics collection during workflow execution."""
        
        # Setup mocks
        mock_detect_intents_use_case.execute.return_value = sample_intent_result
        mock_similarity_search_use_case.search_similar_categories.return_value = sample_rag_result
        
        # Create orchestrator
        orchestrator = ClassifyComplaintsUseCase(
            detect_intents_use_case=mock_detect_intents_use_case,
            similarity_search_use_case=mock_similarity_search_use_case,
            classification_config=classification_config,
            enable_llm_analysis=True
        )
        
        # Mock the LLM classifier
        with patch.object(orchestrator._llm_classifier, 'classify_with_context') as mock_classify:
            mock_classify.return_value = ClassificationResult(
                case_id="TEST-001",
                main_category="交通號誌、標誌、標線及大眾運輸",
                sub_category="交通標誌牌面、反射鏡損壞傾斜",
                sub_description="測試分類",
                intents=[IntentType.INFRASTRUCTURE_REPAIR],
                confidence=ConfidenceLevel.HIGH,
                reasoning="測試用分類結果",
                rag_candidates=sample_rag_result.candidates,
                similarity_score=0.82,
                processing_time_ms=250
            )
            
            # Execute workflow
            result = await orchestrator.execute(sample_complaint)
            
            # Get performance metrics
            metrics = orchestrator.get_performance_metrics()
            
            # Verify metrics collection
            assert metrics["total_processed"] == 1
            assert metrics["success_rate"] == 1.0
            assert metrics["high_confidence_rate"] == 1.0
            assert metrics["avg_total_time_ms"] > 0
            assert metrics["avg_intent_time_ms"] > 0
            assert metrics["avg_rag_time_ms"] > 0
            assert metrics["avg_llm_time_ms"] > 0


if __name__ == "__main__":
    # Run tests with asyncio support
    pytest.main([__file__, "-v"])