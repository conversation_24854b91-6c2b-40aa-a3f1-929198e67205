"""Unit tests for intent detection domain entities."""

import pytest
from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.detected_intent import DetectedIntent
from src.domain.entities.intent_detection_result import IntentDetectionResult


class TestIntentDetectionRequest:
    """Test intent detection request entity."""
    
    def test_create_valid_request(self):
        """Test creating a valid intent detection request."""
        request = IntentDetectionRequest(
            complaint_id="test-001",
            complaint_subject="測試陳情",
            complaint_content="這是測試陳情內容",
            metadata={"source": "test"}
        )
        
        assert request.complaint_id == "test-001"
        assert request.complaint_subject == "測試陳情"
        assert request.complaint_content == "這是測試陳情內容"
        assert request.metadata == {"source": "test"}

    def test_create_request_without_metadata(self):
        """Test creating request without optional metadata."""
        request = IntentDetectionRequest(
            complaint_id="test-002",
            complaint_subject="測試",
            complaint_content="測試內容"
        )
        
        assert request.complaint_id == "test-002"
        assert request.metadata is None


class TestDetectedIntent:
    """Test detected intent entity."""
    
    def test_create_predefined_intent(self):
        """Test creating a predefined intent."""
        intent = DetectedIntent(
            intent_name="檢舉告發",
            is_predefined=True,
            reasoning="文中包含檢舉違法行為的表述",
            text_evidence=["我要檢舉某公務員", "違法行為"]
        )
        
        assert intent.intent_name == "檢舉告發"
        assert intent.is_predefined is True
        assert len(intent.text_evidence) == 2
        assert "我要檢舉某公務員" in intent.text_evidence

    def test_create_custom_intent(self):
        """Test creating a custom intent."""
        intent = DetectedIntent(
            intent_name="申請相關諮詢",
            is_predefined=False,
            reasoning="關於申請流程的詢問",
            text_evidence=["申請流程", "如何申請"]
        )
        
        assert intent.intent_name == "申請相關諮詢"
        assert intent.is_predefined is False
        assert intent.reasoning == "關於申請流程的詢問"


class TestIntentDetectionResult:
    """Test intent detection result entity."""
    
    def test_create_successful_result(self):
        """Test creating a successful detection result."""
        intents = [
            DetectedIntent(
                intent_name="請求協助",
                is_predefined=True,
                reasoning="請求政府協助解決問題",
                text_evidence=["請協助", "需要幫助"]
            )
        ]
        
        result = IntentDetectionResult(
            complaint_id="test-003",
            detected_intents=intents,
            content_summary="民眾請求政府協助解決相關問題",
            processing_time_ms=1250
        )
        
        assert result.complaint_id == "test-003"
        assert len(result.detected_intents) == 1
        assert result.detected_intents[0].intent_name == "請求協助"
        assert result.content_summary == "民眾請求政府協助解決相關問題"
        assert result.processing_time_ms == 1250
        assert result.error_message is None

    def test_create_error_result(self):
        """Test creating an error result."""
        result = IntentDetectionResult(
            complaint_id="test-004",
            detected_intents=[],
            content_summary="",
            processing_time_ms=0,
            error_message="分析失敗：API連線錯誤"
        )
        
        assert result.complaint_id == "test-004"
        assert len(result.detected_intents) == 0
        assert result.content_summary == ""
        assert result.error_message == "分析失敗：API連線錯誤"

    def test_multi_intent_result(self):
        """Test result with multiple detected intents."""
        intents = [
            DetectedIntent("抱怨問題", True, "對服務不滿", ["服務差"]),
            DetectedIntent("請求改善", True, "希望改善服務", ["希望改善"]),
            DetectedIntent("自定義意圖", False, "特殊需求", ["特殊情況"])
        ]
        
        result = IntentDetectionResult(
            complaint_id="test-multi",
            detected_intents=intents,
            content_summary="民眾對服務不滿並要求改善，有特殊需求",
            processing_time_ms=1800
        )
        
        assert len(result.detected_intents) == 3
        assert result.detected_intents[0].is_predefined is True
        assert result.detected_intents[2].is_predefined is False