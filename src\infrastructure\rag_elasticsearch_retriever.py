import asyncio
import logging
import time
from typing import List

from ..config.settings import Settings
from ..domain.entities.rag_candidate import RAGCandidate
from ..domain.entities.rag_search import RAGSearchMetadata, RAGSearchResult
from ..domain.interfaces.rag_retriever import RAGRetrieverInterface
from .embedders.gemini_embedder import GeminiEmbedder
from .exceptions import ElasticsearchConnectionError
from .repositories.elasticsearch_repository import ElasticsearchRepository

# 設定日誌
logger = logging.getLogger(__name__)


class RAGElasticsearchRetriever(ElasticsearchRepository, RAGRetrieverInterface):
    """Elasticsearch-based implementation of RAG similarity search."""

    def __init__(self, settings: Settings, embedder: GeminiEmbedder):
        super().__init__(settings)
        self._embedder = embedder
        self.settings = settings

    async def search_similar_categories(
        self,
        query_text: str,
        similarity_threshold: float = 0.75,
        fallback_threshold: float = 0.6,
        max_results: int = 5,
    ) -> RAGSearchResult:
        """
        Implementation of semantic similarity search.

        Algorithm:
        1. Validate input parameters
        2. Generate query embedding using GeminiEmbedder
        3. Execute parallel cosine similarity queries on both indices
        4. Apply primary threshold filtering
        5. If no results, retry with fallback threshold
        6. Rank and limit results
        7. Return structured RAGSearchResult with metadata
        """
        start_time = time.time()

        # Input validation
        if not query_text or not query_text.strip():
            raise ValueError("Query text cannot be empty")

        if not 0.0 <= fallback_threshold <= similarity_threshold <= 1.0:
            raise ValueError(
                "Invalid threshold values: fallback_threshold must be <= similarity_threshold"
            )

        if max_results <= 0:
            raise ValueError("max_results must be greater than 0")

        # Step 1: Generate embedding
        embedding_start = time.time()
        try:
            query_embedding = await self._embedder.generate_embedding(
                query_text, task_type="RETRIEVAL_QUERY"
            )
        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            raise ElasticsearchConnectionError(
                host=self.settings.elasticsearch.url, original_error=e
            )

        embedding_time = (time.time() - embedding_start) * 1000

        # Step 2: Execute parallel similarity searches
        es_start = time.time()
        sub_categories_task = self._search_index_similarity(
            self.settings.elasticsearch.sub_categories_index,
            query_embedding,
            similarity_threshold,
            max_results,
        )
        main_categories_task = self._search_index_similarity(
            self.settings.elasticsearch.main_categories_index,
            query_embedding,
            similarity_threshold,
            max_results,
        )

        sub_results, main_results = await asyncio.gather(
            sub_categories_task, main_categories_task, return_exceptions=True
        )

        # Handle search exceptions
        if isinstance(sub_results, Exception):
            logger.error(f"Sub-categories search failed: {str(sub_results)}")
            sub_results = []
        if isinstance(main_results, Exception):
            logger.error(f"Main-categories search failed: {str(main_results)}")
            main_results = []

        es_time = (time.time() - es_start) * 1000

        # Step 3: Combine and filter results
        all_candidates = sub_results + main_results
        used_fallback = False

        # Step 4: Apply fallback if needed
        if not all_candidates:
            logger.info(
                f"No results found with primary threshold {similarity_threshold}, trying fallback {fallback_threshold}"
            )
            used_fallback = True
            sub_fallback_task = self._search_index_similarity(
                self.settings.elasticsearch.sub_categories_index,
                query_embedding,
                fallback_threshold,
                max_results,
            )
            main_fallback_task = self._search_index_similarity(
                self.settings.elasticsearch.main_categories_index,
                query_embedding,
                fallback_threshold,
                max_results,
            )

            sub_fallback, main_fallback = await asyncio.gather(
                sub_fallback_task, main_fallback_task, return_exceptions=True
            )

            if not isinstance(sub_fallback, Exception):
                all_candidates.extend(sub_fallback)
            if not isinstance(main_fallback, Exception):
                all_candidates.extend(main_fallback)

        # Step 5: Rank and limit final results
        final_candidates = sorted(
            all_candidates, key=lambda x: x.similarity_score, reverse=True
        )[: max_results * 2]  # Allow more candidates for better selection

        total_time = (time.time() - start_time) * 1000

        # Step 6: Create metadata
        metadata = RAGSearchMetadata(
            embedding_generation_time_ms=embedding_time,
            elasticsearch_query_time_ms=es_time,
            total_search_time_ms=total_time,
            primary_threshold_used=similarity_threshold,
            fallback_threshold_used=fallback_threshold if used_fallback else None,
            total_candidates_found=len(final_candidates),
            sub_categories_searched=len(
                [
                    c
                    for c in final_candidates
                    if c.index_source
                    == self.settings.elasticsearch.sub_categories_index
                ]
            ),
            main_categories_searched=len(
                [
                    c
                    for c in final_candidates
                    if c.index_source
                    == self.settings.elasticsearch.main_categories_index
                ]
            ),
        )

        logger.info(
            f"RAG search completed: {len(final_candidates)} candidates found in {total_time:.2f}ms"
        )

        return RAGSearchResult(
            candidates=final_candidates,
            used_fallback_threshold=used_fallback,
            search_metadata=metadata,
        )

    async def _search_index_similarity(
        self,
        index_name: str,
        query_embedding: List[float],
        threshold: float,
        max_results: int,
    ) -> List[RAGCandidate]:
        """Execute cosine similarity search on a specific index."""

        # Use script_score query for cosine similarity
        query = {
            "size": max_results,
            "min_score": threshold + 1.0,  # Add 1.0 because script_score adds 1.0
            "query": {
                "script_score": {
                    "query": {"match_all": {}},
                    "script": {
                        "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                        "params": {"query_vector": query_embedding},
                    },
                }
            },
            "_source": [
                "main_category",
                "sub_category",
                "description",
                "sub_description",
                "main_description",
                "keywords",
                "combined_text",
            ],
        }

        try:
            response = await self.client.search(index=index_name, body=query)
            candidates = []

            for hit in response["hits"]["hits"]:
                # Normalize score back to 0-1 range (script_score adds 1.0)
                similarity_score = hit["_score"] - 1.0
                source_data = hit["_source"]

                # Generate category_id based on index and source data
                if index_name == self.settings.elasticsearch.sub_categories_index:
                    category_id = f"{source_data.get('main_category', '')}_{source_data.get('sub_category', '')}"
                    category_type = source_data.get("sub_category", "")
                else:
                    category_id = source_data.get("main_category", "")
                    category_type = source_data.get("main_category", "")

                candidate = RAGCandidate(
                    category_id=category_id,
                    similarity_score=round(similarity_score, 4),
                    category_data=source_data,
                    index_source=index_name,
                    category_type=category_type,
                    keywords=source_data.get("keywords", []),
                    combined_text=source_data.get("combined_text", ""),
                    # Legacy fields for backward compatibility
                    main_category=source_data.get("main_category"),
                    sub_category=source_data.get("sub_category"),
                    sub_description=source_data.get("sub_description"),
                    main_description=source_data.get("main_description"),
                )
                candidates.append(candidate)

            logger.debug(
                f"Found {len(candidates)} candidates in {index_name} with threshold {threshold}"
            )
            return candidates

        except Exception as e:
            logger.error(f"Search failed for index {index_name}: {str(e)}")
            # Return empty list for individual index failures to allow graceful degradation
            return []
