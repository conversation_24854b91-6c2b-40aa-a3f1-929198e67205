"""Interface for classification services."""

from abc import ABC, abstractmethod
from typing import List, Optional

from ..entities.classification_result import ClassificationResult
from ..entities.intent_type import IntentType
from ..entities.rag_candidate import RAGCandidate


class ClassificationServiceInterface(ABC):
    """Abstract interface for complaint classification services."""

    @abstractmethod
    async def classify_with_context(
        self,
        complaint_input: str,
        rag_candidates: List[RAGCandidate],
        detected_intents: List[IntentType],
        summary: Optional[str] = None,
        case_id: Optional[str] = None,
    ) -> ClassificationResult:
        """
        Classify a complaint with contextual information.

        Args:
            complaint_input: Original complaint text
            rag_candidates: Similar categories from vector search
            detected_intents: List of detected intents
            summary: Optional content summary
            case_id: Optional case identifier

        Returns:
            ClassificationResult with classification details
        """
        pass