"""Elasticsearch configuration settings."""

from typing import Optional

from pydantic import Field

# from .base_llm_config import BaseConfig
from pydantic_settings import BaseSettings, SettingsConfigDict


class ElasticsearchSettings(BaseSettings):
    """Elasticsearch connection and index configuration."""

    # Connection Settings
    url: str = Field(..., env="ELASTICSEARCH_URL")
    username: Optional[str] = Field(..., env="ELASTICSEARCH_USERNAME")
    password: Optional[str] = Field(..., env="ELASTICSEARCH_PASSWORD")

    # Connection Pool Settings
    max_connections: int = Field(default=10, ge=1, le=100)
    connection_timeout: float = Field(default=30.0, ge=1.0, le=120.0)

    # Index Settings
    main_categories_index: str = Field(
        default="main_categories", env="MAIN_CATEGORIES_INDEX"
    )
    sub_categories_index: str = Field(
        default="sub_categories", env="SUB_CATEGORIES_INDEX"
    )

    # Embedding Settings
    embedding_dims: int = Field(default=3072, env="EMBEDDING_DIMS", ge=1)
    embedding_task_type: str = Field(
        default="RETRIEVAL_DOCUMENT", env="EMBEDDING_TASK_TYPE"
    )

    # Search Settings
    default_search_size: int = Field(default=10, ge=1, le=100)
    max_search_size: int = Field(default=100, ge=1, le=1000)

    def get_connection_config(self) -> dict:
        """Get Elasticsearch connection configuration."""
        config = {
            "hosts": [self.url],
            "timeout": self.connection_timeout,
            "max_retries": 3,
            "retry_on_timeout": True,
        }

        if self.username and self.password:
            config["http_auth"] = (self.username, self.password)

        return config

    def get_index_settings(self) -> dict:
        """Get index configuration settings."""
        return {
            "main_categories": self.main_categories_index,
            "sub_categories": self.sub_categories_index,
        }

    model_config = SettingsConfigDict(
        extra="ignore",  # Ignore extra fields for backward compatibility
        case_sensitive=False,
        env_file=".env",
        env_file_encoding="utf-8",
    )
