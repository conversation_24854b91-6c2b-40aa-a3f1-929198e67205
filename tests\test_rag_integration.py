"""Integration tests for RAG similarity search functionality."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch, MagicMock
from typing import List
from pathlib import Path

from src.infrastructure.rag_elasticsearch_retriever import RAGElasticsearchRetriever
from src.infrastructure.embedders.gemini_embedder import <PERSON><PERSON>mbedder
from src.application.use_cases.similarity_search import SimilaritySearchUseCase
from src.config.settings import Settings, RAGSearchSettings
from src.domain.entities.rag_search import RAGSearchResult, RAGSearchMetadata
from src.domain.entities.rag_candidate import RAGCandidate


@pytest.fixture
def mock_settings():
    """Create mock settings for integration tests."""
    settings = Mock(spec=Settings)
    settings.elasticsearch_url = "http://localhost:9200"
    settings.elasticsearch_username = None
    settings.elasticsearch_password = None
    settings.environment = "local"
    
    # Mock project_root to support pathlib operations
    mock_path = MagicMock(spec=Path)
    mock_path.__truediv__ = lambda self, other: mock_path  # Support / operator
    mock_path.exists.return_value = False  # No cert file exists
    settings.project_root = mock_path
    
    settings.sub_categories_index = "sub_categories"
    settings.main_categories_index = "main_categories"
    settings.rag_search = RAGSearchSettings(
        primary_similarity_threshold=0.75,
        fallback_similarity_threshold=0.6,
        max_candidates_per_index=5
    )
    settings.embedding_task_type = "RETRIEVAL_DOCUMENT"
    return settings


@pytest.fixture
def mock_embedder():
    """Create mock GeminiEmbedder."""
    embedder = AsyncMock(spec=GeminiEmbedder)
    # Return a mock 3072-dimensional embedding vector
    embedder.generate_embedding.return_value = [0.1] * 3072
    return embedder


@pytest.fixture
def mock_elasticsearch_response():
    """Create mock Elasticsearch search response."""
    return {
        "hits": {
            "hits": [
                {
                    "_score": 1.85,  # Will be normalized to 0.85
                    "_source": {
                        "main_category": "交通設施",
                        "sub_category": "交通號誌",
                        "description": "交通號誌相關問題",
                        "keywords": ["交通", "號誌", "標誌"],
                        "combined_text": "交通設施 交通號誌 標誌牌面損壞"
                    }
                },
                {
                    "_score": 1.78,  # Will be normalized to 0.78
                    "_source": {
                        "main_category": "道路維護", 
                        "description": "道路維護相關問題",
                        "keywords": ["道路", "維護", "修繕"],
                        "combined_text": "道路維護 路面坑洞修繕"
                    }
                }
            ]
        }
    }


class TestRAGIntegration:
    """Integration tests for complete RAG search workflow."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_similarity_search(
        self, 
        mock_settings, 
        mock_embedder, 
        mock_elasticsearch_response
    ):
        """Test complete end-to-end similarity search workflow."""
        # Setup mocked Elasticsearch client
        with patch('src.infrastructure.rag_elasticsearch_retriever.AsyncElasticsearch') as mock_es_class:
            mock_es_client = AsyncMock()
            mock_es_class.return_value = mock_es_client
            mock_es_client.search.return_value = mock_elasticsearch_response
            
            # Create retriever with mocked dependencies
            retriever = RAGElasticsearchRetriever(mock_settings, mock_embedder)
            retriever.client = mock_es_client  # Override with mock
            
            # Create use case
            use_case = SimilaritySearchUseCase(retriever, mock_settings)
            
            # Execute search
            query_text = "交通號誌標誌牌面損壞"
            result = await use_case.search_similar_categories(query_text)
            
            # Verify results
            assert isinstance(result, RAGSearchResult)
            assert len(result.candidates) == 4  # 2 from each index (sub + main)
            assert result.used_fallback_threshold is False
            assert result.has_high_confidence_matches is True
            
            # Verify best candidate
            best_candidate = result.best_candidate
            assert best_candidate is not None
            assert best_candidate.similarity_score == 0.85
            assert "交通號誌" in best_candidate.category_type
            
            # Verify metadata
            metadata = result.search_metadata
            assert metadata.total_candidates_found == 4
            assert metadata.primary_threshold_used == 0.75
            assert metadata.fallback_threshold_used is None
            assert metadata.total_search_time_ms >= 0  # May be 0 in mocked tests
            
            # Verify embedding generation was called with correct task_type
            mock_embedder.generate_embedding.assert_called_once_with(
                query_text, 
                task_type="RETRIEVAL_QUERY"
            )
            
            # Verify Elasticsearch searches were called (2 times - both indices)
            assert mock_es_client.search.call_count == 2
    
    @pytest.mark.asyncio
    async def test_fallback_threshold_behavior(
        self, 
        mock_settings, 
        mock_embedder
    ):
        """Test fallback threshold behavior when no high-confidence matches."""
        # Setup mocked Elasticsearch client with no results for primary search
        with patch('src.infrastructure.rag_elasticsearch_retriever.AsyncElasticsearch') as mock_es_class:
            mock_es_client = AsyncMock()
            mock_es_class.return_value = mock_es_client
            
            # Primary search returns empty results
            empty_response = {"hits": {"hits": []}}
            
            # Fallback search returns results
            fallback_response = {
                "hits": {
                    "hits": [
                        {
                            "_score": 1.65,  # Will be normalized to 0.65
                            "_source": {
                                "main_category": "其他",
                                "description": "其他問題",
                                "keywords": ["其他"],
                                "combined_text": "其他相關問題"
                            }
                        }
                    ]
                }
            }
            
            # Configure mock to return empty for primary, results for fallback
            mock_es_client.search.side_effect = [
                empty_response,  # sub_categories primary
                empty_response,  # main_categories primary
                fallback_response,  # sub_categories fallback
                fallback_response   # main_categories fallback
            ]
            
            # Create retriever with mocked dependencies
            retriever = RAGElasticsearchRetriever(mock_settings, mock_embedder)
            retriever.client = mock_es_client
            
            # Execute search
            result = await retriever.search_similar_categories("模糊查詢內容")
            
            # Verify fallback was used
            assert result.used_fallback_threshold is True
            assert len(result.candidates) == 2  # One from each fallback search
            assert result.candidates[0].similarity_score == 0.65
            assert result.search_metadata.fallback_threshold_used == 0.6
            
            # Verify 4 searches were made (2 primary + 2 fallback)
            assert mock_es_client.search.call_count == 4
    
    @pytest.mark.asyncio
    async def test_high_confidence_via_equal_thresholds(
        self, 
        mock_settings, 
        mock_embedder, 
        mock_elasticsearch_response
    ):
        """Test high-confidence search by setting equal thresholds."""
        with patch('src.infrastructure.rag_elasticsearch_retriever.AsyncElasticsearch') as mock_es_class:
            mock_es_client = AsyncMock()
            mock_es_class.return_value = mock_es_client
            mock_es_client.search.return_value = mock_elasticsearch_response
            
            # Create components
            retriever = RAGElasticsearchRetriever(mock_settings, mock_embedder)
            retriever.client = mock_es_client
            use_case = SimilaritySearchUseCase(retriever, mock_settings)
            
            # Execute high-confidence search by setting equal thresholds
            result = await use_case.search_similar_categories(
                query_text="交通問題",
                similarity_threshold=0.8,
                fallback_threshold=0.8  # Same threshold = no fallback
            )
            
            # Verify no fallback was used
            assert result.used_fallback_threshold is False
            assert len(result.high_confidence_candidates) > 0
    
    @pytest.mark.asyncio  
    async def test_error_handling_in_integration(
        self, 
        mock_settings, 
        mock_embedder
    ):
        """Test error handling across the integration."""
        # Test embedding generation failure
        mock_embedder.generate_embedding.side_effect = Exception("Gemini API error")
        
        with patch('src.infrastructure.rag_elasticsearch_retriever.AsyncElasticsearch') as mock_es_class:
            mock_es_client = AsyncMock()
            mock_es_class.return_value = mock_es_client
            
            retriever = RAGElasticsearchRetriever(mock_settings, mock_embedder)
            retriever.client = mock_es_client
            use_case = SimilaritySearchUseCase(retriever, mock_settings)
            
            # Execute and expect error propagation
            with pytest.raises(Exception):  # Should be ElasticsearchConnectionError wrapping the original
                await use_case.search_similar_categories("test query")