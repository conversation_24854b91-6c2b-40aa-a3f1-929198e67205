---
title: Technology Stack
description: "Documents the complete technology stack, dependencies, and technical architecture decisions."
inclusion: always
---

# Technology Stack

## Core Technologies

### Runtime Environment

-   **Python 3.12**: Required version for modern async/await features and type hints
-   **uv**: Modern Python package and environment manager (replaces pip/pipenv)
-   **Windows**: Primary development and deployment platform

### AI & Machine Learning

-   **Google Generative AI (Gemini)**: Primary embedding service
    -   Model: `gemini-embedding-001`
    -   Vector Dimensions: 3072
    -   Task Type: `RETRIEVAL_DOCUMENT`
    -   Async integration with rate limiting

### Database & Search

-   **Elasticsearch 8.13.2**: Vector database and search engine
    -   Dense vector storage for embeddings
    -   Traditional Chinese text analysis
    -   Multi-environment configuration with SSL/TLS support
    -   Bulk indexing with error handling

### Core Dependencies

```toml
[dependencies]
elasticsearch = "8.13.2"           # Vector database client
google-generativeai = ">=0.5.0"   # Gemini API integration
python-dotenv = ">=1.0.0"         # Environment variable management
pydantic = ">=2.0.0"              # Data validation and serialization
pydantic-settings = ">=2.0.0"     # Settings management
aiohttp = ">=3.12.15"             # Async HTTP client
```

## Architecture Patterns

### Clean Architecture Implementation

-   **Domain Layer**: Business entities and interfaces (dependency-free)
-   **Application Layer**: Use cases and business logic orchestration
-   **Infrastructure Layer**: External system integrations (Gemini, Elasticsearch)
-   **Configuration Layer**: Environment-aware settings management

### Async/Await Patterns

-   **Async Embedding Generation**: Batch processing with configurable concurrency
-   **Async Repository Operations**: Non-blocking Elasticsearch interactions
-   **Error Handling**: Comprehensive async exception management
-   **Resource Management**: Proper connection lifecycle management

## Development Tools

### Package Management

```bash
uv sync                    # Install dependencies
uv add <package>          # Add new dependency
uv run src/main.py        # Run application
uv lock --upgrade         # Update dependencies
```

### Environment Configuration

-   **Multi-environment Support**: local, dev, prod
-   **SSL/TLS Certificates**: Environment-specific CA certificates
-   **Credential Management**: .env file with secure defaults

## Data Processing Architecture

### Embedding Pipeline

1. **JSON Data Loading**: Taiwan government complaint categories
2. **Text Preprocessing**: Structured text formatting for embedding generation
3. **Batch Embedding**: Async generation with rate limiting
4. **Vector Indexing**: Elasticsearch dense vector storage

### Data Processing Architecture

#### Text Processing Pipeline

1. **JSON Data Loading**: Taiwan government complaint categories
2. **Structured Text Formatting**: Creates combined text for embedding generation
    - Main categories: "主案類: {name}\n 描述: {description}"
    - Sub categories: "子案類: {name}\n 描述: {description}\n 所屬主案類: {parent}"
3. **Batch Embedding Generation**: Async processing with rate limiting
4. **Vector Indexing**: Elasticsearch dense vector storage with Chinese text analysis

#### Current Processing Limitations

-   Keyword extraction uses regex patterns (basic implementation)
-   Single environment processing per execution

## Elasticsearch Configuration

### Index Schemas

#### Main Categories Index

```json
{
	"mappings": {
		"properties": {
			"main_category": { "type": "keyword" },
			"description": { "type": "text", "analyzer": "ik_max_word" },
			"combined_text": { "type": "text", "analyzer": "ik_max_word" },
			"sub_categories": { "type": "keyword" },
			"embedding": {
				"type": "dense_vector",
				"dims": 3072,
				"index": true,
				"similarity": "cosine"
			},
			"created_at": { "type": "date", "format": "yyyy/MM/dd HH:mm:ss" }
		}
	}
}
```

#### Sub Categories Index

```json
{
	"mappings": {
		"properties": {
			"main_category": { "type": "keyword" },
			"sub_category": { "type": "keyword" },
			"sub_description": { "type": "text", "analyzer": "ik_max_word" },
			"main_description": { "type": "text", "analyzer": "ik_max_word" },
			"combined_text": { "type": "text", "analyzer": "ik_max_word" },
			"keywords": { "type": "keyword" },
			"embedding": {
				"type": "dense_vector",
				"dims": 3072,
				"index": true,
				"similarity": "cosine"
			},
			"created_at": { "type": "date", "format": "yyyy/MM/dd HH:mm:ss" }
		}
	}
}
```

## Error Handling & Logging

### Custom Exception Classes

-   `ElasticsearchConnectionError`: Connection and authentication issues
-   `ElasticsearchBulkError`: Batch indexing failures with detailed reporting
-   Comprehensive error context and recovery suggestions

### Logging Strategy

-   **Multi-handler Setup**: File logging (`app.log`) + console output
-   **UTF-8 Encoding**: Proper Chinese character support
-   **Structured Logging**: Contextual information for debugging
-   **Performance Monitoring**: Batch processing metrics

## Security & Configuration

### Environment Variables

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Elasticsearch
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=optional
ELASTICSEARCH_PASSWORD=optional

# Optional Configuration
MAIN_CATEGORIES_INDEX=main_categories
SUB_CATEGORIES_INDEX=sub_categories
EMBEDDING_DIMS=3072
EMBEDDING_TASK_TYPE=RETRIEVAL_DOCUMENT
ENVIRONMENT=local
```

### SSL/TLS Configuration

-   Environment-specific CA certificates in `src/certs/es/{env}/ca/`
-   Configurable SSL verification
-   Secure credential management

## Performance Considerations

### Batch Processing

-   Configurable batch sizes for embedding generation
-   Rate limiting to respect API quotas
-   Async processing for optimal throughput

### Elasticsearch Optimization

-   Bulk indexing with error recovery
-   Connection pooling and lifecycle management
-   Index optimization for vector search performance

## Current Implementation Status

### Completed Components

-   **Data Ingestion Pipeline**: Full JSON to Elasticsearch workflow
-   **Clean Architecture**: Domain, application, and infrastructure layers
-   **Async Processing**: Batch embedding generation with proper error handling
-   **Environment Configuration**: Multi-environment setup with SSL support
-   **Comprehensive Error Handling**: Custom exceptions with detailed diagnostics

### Known Limitations

-   **Basic Keyword Extraction**: Regex-based approach for keywords
-   **Single Index Management**: Creates indices without version management
-   **No Query Interface**: Pure data ingestion without search capabilities

### Future Technical Considerations

-   **Advanced Text Processing**: Improve keyword extraction beyond regex
-   **Search Query Implementation**: Add vector similarity search capabilities
-   **Production Deployment**: Docker containerization and monitoring
