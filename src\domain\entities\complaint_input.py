from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator, field_serializer


class ComplaintInput(BaseModel):
    """
    Represents citizen complaint input for classification processing.

    This entity validates and structures incoming complaint data before
    classification processing begins.
    """

    case_id: str = Field(
        ...,
        description="唯一投訴識別碼",
        min_length=1,
        max_length=100
    )

    subject: str = Field(
        ...,
        description="投訴標題或主旨",
        min_length=1,
        max_length=200
    )

    content: str = Field(
        ...,
        description="投訴內容文本",
        min_length=10,
        max_length=10000
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="額外的元數據字段用於處理上下文"
    )

    record_time: datetime = Field(
        default_factory=datetime.now,
        description="接收投訴的時間戳"
    )

    @field_validator('subject')
    @classmethod
    def validate_subject(cls, v: str) -> str:
        """驗證投訴標題是否符合要求"""
        if not v or len(v.strip()) < 1:
            raise ValueError("投訴標題不能為空")
        return v.strip()

    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """驗證投訴內容是否符合最低要求"""
        if not v or len(v.strip()) < 10:
            raise ValueError("投訴內容必須至少包含10個字符")
        return v.strip()

    @field_validator('case_id')
    @classmethod
    def validate_case_id(cls, v: str) -> str:
        """驗證投訴ID格式"""
        if not v or not v.strip():
            raise ValueError("投訴ID不能為空")
        return v.strip()

    @field_serializer('record_time')
    def serialize_record_time(self, value: datetime) -> str:
        """將record_time格式化為Elasticsearch所需的yyyy/MM/dd HH:mm:ss格式"""
        return value.strftime('%Y/%m/%d %H:%M:%S')