"""LLM configuration settings."""

from enum import Enum
from typing import Dict, Optional

from pydantic import Field

from .base_llm_config import BaseConfig, PerformanceConfig


class APIProvider(str, Enum):
    """API providers for LLM services."""

    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"

    @classmethod
    def get_all_providers(cls) -> list[str]:
        """Get all available provider values."""
        return [provider.value for provider in cls]

    @classmethod
    def is_valid_provider(cls, provider: str) -> bool:
        """Check if a provider string is valid."""
        return provider in cls.get_all_providers()


class LLMProviderConfig(BaseConfig):
    """Configuration for a specific LLM provider."""

    provider: APIProvider
    default_model: str
    available_models: list[str] = Field(default_factory=list)
    max_tokens_limit: int = Field(default=4096, ge=1)
    supports_streaming: bool = Field(default=True)
    api_key: Optional[str] = Field(default=None)

    def model_post_init(self, __context) -> None:
        """Post-initialization validation."""
        # Add default model to available models if not present
        if self.default_model not in self.available_models:
            self.available_models.insert(0, self.default_model)

    def is_available(self) -> bool:
        """Check if provider is available (has API key)."""
        return self.api_key is not None and self.api_key.strip() != ""


class LLMSettings(BaseConfig):
    """Centralized LLM configuration."""

    # Provider Configurations
    providers: Dict[str, LLMProviderConfig] = Field(default_factory=dict)
    
    # Default provider
    default_provider: APIProvider = Field(default=APIProvider.GOOGLE)
    
    # Performance settings
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    
    # LLM Generation Parameters
    temperature: float = Field(default=0.1, ge=0.0, le=2.0)
    max_tokens: int = Field(default=1000, ge=100, le=8192)

    def model_post_init(self, __context) -> None:
        """Initialize default providers if not provided."""
        if not self.providers:
            self._initialize_default_providers()

    def _initialize_default_providers(self) -> None:
        """Initialize default provider configurations."""
        self.providers = {
            APIProvider.GOOGLE.value: LLMProviderConfig(
                provider=APIProvider.GOOGLE,
                default_model="gemini-2.5-flash",
                available_models=["gemini-2.5-flash"],
                max_tokens_limit=8192,
                supports_streaming=True,
            ),
            APIProvider.OPENAI.value: LLMProviderConfig(
                provider=APIProvider.OPENAI,
                default_model="gpt-4.1-mini",
                available_models=["gpt-4.1-mini"],
                max_tokens_limit=4096,
                supports_streaming=True,
            ),
            APIProvider.ANTHROPIC.value: LLMProviderConfig(
                provider=APIProvider.ANTHROPIC,
                default_model="claude-3-sonnet-20240229",
                available_models=[
                    "claude-3-sonnet-20240229",
                    "claude-3-haiku-20240307",
                ],
                max_tokens_limit=4096,
                supports_streaming=True,
            ),
        }

    def set_api_key(self, provider: APIProvider, api_key: Optional[str]) -> None:
        """Set API key for a provider."""
        if provider.value in self.providers:
            self.providers[provider.value].api_key = api_key

    def get_provider_config(self, provider: APIProvider) -> Optional[LLMProviderConfig]:
        """Get provider configuration."""
        return self.providers.get(provider.value)

    def get_api_key(self, provider: APIProvider) -> Optional[str]:
        """Get API key for specified provider."""
        config = self.get_provider_config(provider)
        return config.api_key if config else None

    def get_default_model(self, provider: APIProvider) -> str:
        """Get default model for specified provider."""
        config = self.get_provider_config(provider)
        return config.default_model if config else "gemini-2.5-flash"

    def is_provider_available(self, provider: APIProvider) -> bool:
        """Check if provider is available (has API key and configuration)."""
        config = self.get_provider_config(provider)
        return config is not None and config.is_available()

    def get_available_providers(self) -> list[APIProvider]:
        """Get list of providers with valid API keys."""
        return [
            APIProvider(provider_name)
            for provider_name, config in self.providers.items()
            if config.is_available()
        ]
