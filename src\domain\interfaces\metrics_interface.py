"""Interface for metrics collection."""

from abc import ABC, abstractmethod
from typing import Dict, Any

from ..entities.classification_result import ClassificationResult


class MetricsInterface(ABC):
    """Abstract interface for classification metrics collection."""

    @abstractmethod
    def record_successful_classification(
        self,
        result: ClassificationResult,
        intent_time: int,
        rag_time: int,
        llm_time: int,
    ) -> None:
        """Record metrics for a successful classification."""
        pass

    @abstractmethod
    def record_failed_classification(self) -> None:
        """Record a failed classification."""
        pass

    @abstractmethod
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        pass