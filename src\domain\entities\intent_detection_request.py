from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class IntentDetectionRequest:
    """
    Request entity for intent detection and content summarization analysis.
    
    Contains all necessary information to perform dual-task processing:
    intent detection and content summarization for complaint text.
    """
    
    complaint_id: str
    complaint_subject: str
    complaint_content: str
    metadata: Optional[Dict[str, Any]] = None