from dataclasses import dataclass
from typing import List


@dataclass
class DetectedIntent:
    """
    Represents a single detected intent from complaint analysis.
    
    Contains the intent classification, validation information,
    reasoning, and supporting evidence from the text.
    """
    
    intent_name: str
    is_predefined: bool  # True for Taiwan govt predefined intents
    reasoning: str
    text_evidence: List[str]  # Specific text snippets supporting this intent