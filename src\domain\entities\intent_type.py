from enum import Enum


class IntentType(str, Enum):
    """
    Taiwan government complaint intent types enumeration.

    Defines all possible intent categories that can be detected
    in citizen complaints based on Taiwan government classification.
    """

    REPORT_VIOLATION = "檢舉告發"
    REQUEST_ASSISTANCE = "請求協助"
    COMPLAINT_PROBLEM = "抱怨問題"
    REQUEST_IMPROVEMENT = "請求改善"
    CONSULTATION_INQUIRY = "諮詢問題"
    DISSATISFIED_SERVICE_ATTITUDE = "不滿服務態度"
    DISSATISFIED_STAFF_PROFESSIONALISM = "不滿人員專業度"
    GRATITUDE_PRAISE = "感謝讚美"
    SUGGEST_PROPOSAL = "提出建議"
    OTHER = "其他"

    @classmethod
    def get_all_intents(cls) -> list[str]:
        """返回所有可用意圖類型的列表"""
        return [intent.value for intent in cls]

    @classmethod
    def is_valid_intent(cls, intent: str) -> bool:
        """檢查給定字符串是否為有效意圖類型"""
        return intent in cls.get_all_intents()
