from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_serializer

from .confidence_level import ConfidenceLevel
from .intent_type import IntentType
from .rag_candidate import RAGCandidate


class ClassificationResult(BaseModel):
    """
    Represents the complete classification result for a citizen complaint.

    Contains the final category assignment, detected intents, confidence
    assessment, and reasoning for the classification decision.
    """

    case_id: str = Field(..., description="原始投訴識別碼")

    # Primary classification results
    main_category: str = Field(..., description="分配的主案類")
    sub_category: str = Field(..., description="分配的子案類")
    sub_description: str = Field(..., description="子案類描述")

    # Multi-intent detection results
    intents: List[IntentType] = Field(
        default_factory=list, description="檢測到的意圖類型列表"
    )

    # Confidence and reasoning
    confidence: ConfidenceLevel = Field(..., description="分類信心等級")
    reasoning: str = Field(..., description="分類決策的說明理由")

    # Supporting data
    rag_candidates: List[RAGCandidate] = Field(
        default_factory=list, description="RAG相似性搜索候選結果"
    )

    similarity_score: float = Field(..., description="最高相似性分數", ge=0.0, le=1.0)

    # Metadata
    processed_at: datetime = Field(
        default_factory=datetime.now, description="處理完成時間戳"
    )

    processing_time_ms: Optional[int] = Field(
        None, description="處理時間（毫秒）", ge=0
    )

    @field_serializer("processed_at")
    def serialize_processed_at(self, value: datetime) -> str:
        """將processed_at格式化為Elasticsearch所需的yyyy/MM/dd HH:mm:ss格式"""
        return value.strftime("%Y/%m/%d %H:%M:%S")

    @field_serializer("intents")
    def serialize_intents(self, value: List[IntentType]) -> List[str]:
        """將意圖枚舉轉換為字符串列表"""
        return [
            intent.value if hasattr(intent, "value") else str(intent)
            for intent in value
        ]

    class Config:
        """Pydantic configuration"""

        validate_assignment = True
        use_enum_values = True
