"""Enumeration for LLM providers."""

from enum import Enum


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    
    OPENAI = "openai"
    GOOGLE = "google"
    ANTHROPIC = "anthropic"

    @classmethod
    def get_all_providers(cls) -> list:
        """Get all available provider values."""
        return [provider.value for provider in cls]

    @classmethod
    def is_valid_provider(cls, provider: str) -> bool:
        """Check if a provider string is valid."""
        return provider in cls.get_all_providers()