import asyncio
import sys
import logging
from pathlib import Path

# 添加專案根目錄到Python路徑
sys.path.append(str(Path(__file__).parent.parent))

from src.config import Settings
from src.infrastructure.embedders import GeminiEmbedder
from src.infrastructure.repositories import ElasticsearchRepository
from src.application.use_cases import ProcessCategoriesUseCase
from src.infrastructure.exceptions import ElasticsearchBulkError, ElasticsearchConnectionError

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


async def main():
    """主程式進入點"""
    repository = None
    
    try:
        logger.info("開始執行 AI RAG 應用程式")
        
        # 載入設定
        settings = Settings()
        logger.info(f"環境: {settings.environment}")

        # 初始化相依性
        embedder = GeminiEmbedder(settings)
        repository = ElasticsearchRepository(settings)

        # 初始化use case
        use_case = ProcessCategoriesUseCase(embedder, repository)

        # 執行處理流程
        json_file_path = "data/categories_def.json"
        logger.info(f"開始處理檔案: {json_file_path}")
        await use_case.execute(json_file_path)
        
        logger.info("應用程式執行成功完成")

    except ElasticsearchConnectionError as e:
        logger.error(f"Elasticsearch 連線錯誤: {e.message}")
        print(f"\n❌ 無法連線到 Elasticsearch")
        print(f"詳細資訊: {e.details}")
        sys.exit(1)
        
    except ElasticsearchBulkError as e:
        logger.error(f"批次索引錯誤: {e.message}")
        print(f"\n❌ 批次索引失敗")
        print(f"成功: {e.success_count} 筆, 失敗: {e.failed_count} 筆")
        
        # 記錄失敗的文件到日誌
        failed_docs = e.get_failed_documents()
        for doc in failed_docs:
            logger.error(f"失敗文件 - ID: {doc['document_id']}, 錯誤: {doc['error_type']} - {doc['error_reason']}")
        
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"應用程式發生錯誤: {type(e).__name__}: {str(e)}", exc_info=True)
        print(f"\n❌ 錯誤：{type(e).__name__}: {str(e)}")
        sys.exit(1)
        
    finally:
        # 關閉連線
        if repository:
            try:
                await repository.close()
                logger.info("已關閉 Elasticsearch 連線")
            except Exception as e:
                logger.warning(f"關閉連線時發生錯誤: {e}")


if __name__ == "__main__":
    asyncio.run(main())
