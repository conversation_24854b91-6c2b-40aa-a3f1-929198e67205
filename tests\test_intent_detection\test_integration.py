"""Integration tests for intent detection system."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.domain.entities.intent_detection_request import IntentDetectionRequest
from src.domain.entities.intent_type import IntentType
from src.infrastructure.intent_analysis.agno_intent_analyzer import IntentDetectionConfig, AgnoIntentAnalyzer
from src.infrastructure.models.intent_analysis_models import IntentAnalysisResponse, DetectedIntentModel


class TestIntentDetectionIntegration:
    """Integration tests for the complete intent detection workflow."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return IntentDetectionConfig(
            primary_api_key="test-gemini-key",
            fallback_api_key="test-openai-key",
            temperature=0.1,
            max_tokens=1000,
            min_content_length=10
        )

    @pytest.fixture
    def mock_agent_response(self):
        """Create mock Agno agent response."""
        return IntentAnalysisResponse(
            detected_intents=[
                DetectedIntentModel(
                    intent_name="請求協助",
                    is_predefined=True,
                    reasoning="民眾明確請求政府單位協助解決問題",
                    text_evidence=["請協助我處理", "需要幫助"]
                ),
                DetectedIntentModel(
                    intent_name="抱怨問題", 
                    is_predefined=True,
                    reasoning="對現有服務表達不滿",
                    text_evidence=["服務很差", "非常不滿意"]
                )
            ],
            content_summary="民眾對服務品質不滿意，請求政府協助改善處理相關問題。"
        )

    @pytest.mark.asyncio
    async def test_full_workflow_with_mocked_agents(self, config, mock_agent_response):
        """Test complete workflow with mocked Agno agents."""
        
        # Create request
        request = IntentDetectionRequest(
            complaint_id="integration-001",
            complaint_subject="服務協助請求",
            complaint_content="我對於目前的服務品質非常不滿意，請協助我處理相關問題，需要政府單位的幫助。"
        )

        # Mock both primary and fallback agents
        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            # Setup mock agent instance
            mock_agent = Mock()
            mock_agent.run.return_value = mock_agent_response
            mock_agent_class.return_value = mock_agent

            # Create use case
            use_case = DetectIntentsUseCase(config)
            
            # Execute
            result = await use_case.execute(request)

            # Assertions
            assert result.complaint_id == "integration-001"
            assert len(result.detected_intents) == 2
            assert result.error_message is None
            
            # Check intent details
            intent_names = [intent.intent_name for intent in result.detected_intents]
            assert "請求協助" in intent_names
            assert "抱怨問題" in intent_names
            
            # Check that predefined intents are correctly identified
            for intent in result.detected_intents:
                assert intent.is_predefined is True  # Both should be predefined
            
            # Check summary
            assert len(result.content_summary) > 0
            assert "民眾" in result.content_summary
            assert result.processing_time_ms > 0

    @pytest.mark.asyncio
    async def test_primary_agent_failure_fallback(self, config, mock_agent_response):
        """Test fallback behavior when primary agent fails."""
        
        request = IntentDetectionRequest(
            complaint_id="fallback-001",
            complaint_subject="測試fallback",
            complaint_content="這是測試primary agent失敗時的fallback機制運作情況。"
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            # Create two different mock agents
            mock_primary_agent = Mock()
            mock_fallback_agent = Mock()
            
            # Primary agent fails
            mock_primary_agent.run.side_effect = Exception("Gemini API Error")
            
            # Fallback agent succeeds
            mock_fallback_agent.run.return_value = IntentAnalysisResponse(
                detected_intents=[
                    DetectedIntentModel(
                        intent_name="其他",
                        is_predefined=True,
                        reasoning="使用fallback模型分析",
                        text_evidence=["測試", "fallback"]
                    )
                ],
                content_summary="這是使用fallback模型產生的摘要。"
            )
            
            # Configure mock to return different agents for different calls
            mock_agent_class.side_effect = [mock_primary_agent, mock_fallback_agent]
            
            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            # Should get result from fallback agent
            assert result.complaint_id == "fallback-001"
            assert len(result.detected_intents) == 1
            assert result.detected_intents[0].intent_name == "其他"
            assert "fallback" in result.content_summary
            assert result.error_message is None

    @pytest.mark.asyncio
    async def test_both_agents_fail(self, config):
        """Test behavior when both primary and fallback agents fail."""
        
        request = IntentDetectionRequest(
            complaint_id="double-fail-001",
            complaint_subject="雙重失敗測試",
            complaint_content="測試當兩個agent都失敗時的錯誤處理機制。"
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            # Both agents fail
            mock_agent = Mock()
            mock_agent.run.side_effect = Exception("Both APIs failed")
            mock_agent_class.return_value = mock_agent

            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            # Should get error result
            assert result.complaint_id == "double-fail-001"
            assert len(result.detected_intents) == 0
            assert result.content_summary == ""
            assert "意圖檢測失敗" in result.error_message
            assert "Both APIs failed" in result.error_message

    @pytest.mark.asyncio
    async def test_custom_intent_detection(self, config):
        """Test detection of custom intents not in predefined list."""
        
        request = IntentDetectionRequest(
            complaint_id="custom-001",
            complaint_subject="特殊申請需求",
            complaint_content="我需要了解新的數位身分證申請流程，以及相關的法規要求。"
        )

        mock_response = IntentAnalysisResponse(
            detected_intents=[
                DetectedIntentModel(
                    intent_name="數位身分證申請諮詢",
                    is_predefined=False,  # Custom intent
                    reasoning="關於數位身分證申請流程的特殊諮詢",
                    text_evidence=["數位身分證申請流程", "法規要求"]
                )
            ],
            content_summary="民眾詢問數位身分證申請流程及相關法規要求。"
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent.run.return_value = mock_response
            mock_agent_class.return_value = mock_agent

            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            assert result.complaint_id == "custom-001"
            assert len(result.detected_intents) == 1
            assert result.detected_intents[0].intent_name == "數位身分證申請諮詢"
            assert result.detected_intents[0].is_predefined is False  # Should be marked as custom
            assert "數位身分證" in result.content_summary

    @pytest.mark.asyncio
    async def test_intent_validation_with_enum(self, config, mock_agent_response):
        """Test that intent validation works correctly with IntentType enum."""
        
        request = IntentDetectionRequest(
            complaint_id="validation-001", 
            complaint_subject="意圖驗證測試",
            complaint_content="測試IntentType enum的驗證功能是否正常運作。"
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent.run.return_value = mock_agent_response
            mock_agent_class.return_value = mock_agent

            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            # Verify that IntentType validation worked correctly
            for intent in result.detected_intents:
                if intent.intent_name in IntentType.get_all_intents():
                    assert intent.is_predefined is True
                else:
                    assert intent.is_predefined is False

    @pytest.mark.asyncio 
    async def test_summary_length_validation(self, config):
        """Test that content summary meets length requirements."""
        
        request = IntentDetectionRequest(
            complaint_id="summary-001",
            complaint_subject="摘要長度測試", 
            complaint_content="這是一個測試摘要長度是否符合100-300字要求的陳情內容。" * 5
        )

        # Mock response with appropriate summary length
        mock_response = IntentAnalysisResponse(
            detected_intents=[
                DetectedIntentModel(
                    intent_name="其他",
                    is_predefined=True,
                    reasoning="測試用意圖",
                    text_evidence=["測試"]
                )
            ],
            content_summary="這是符合長度要求的內容摘要，" * 10  # Should be ~150 chars
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent.run.return_value = mock_response
            mock_agent_class.return_value = mock_agent

            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            # Check summary length is reasonable (not enforced strictly in code, but should be reasonable)
            assert len(result.content_summary) > 50  # Minimum reasonable length
            assert len(result.content_summary) < 500  # Maximum reasonable length

    @pytest.mark.asyncio
    async def test_metrics_collection_integration(self, config, mock_agent_response):
        """Test that metrics are collected during integration."""
        
        request = IntentDetectionRequest(
            complaint_id="metrics-001",
            complaint_subject="指標收集測試",
            complaint_content="測試在整合流程中是否正確收集效能指標數據。"
        )

        with patch('src.infrastructure.intent_analysis.agno_intent_analyzer.Agent') as mock_agent_class:
            mock_agent = Mock()
            mock_agent.run.return_value = mock_agent_response
            mock_agent_class.return_value = mock_agent

            use_case = DetectIntentsUseCase(config)
            result = await use_case.execute(request)

            # Check that metrics were recorded
            metrics_summary = use_case.get_metrics_summary()
            
            assert metrics_summary["total_analyses"] == 1
            assert metrics_summary["avg_processing_time_ms"] > 0
            assert "請求協助" in metrics_summary["most_common_intents"]
            assert metrics_summary["dual_task_success_rate"] == 1.0