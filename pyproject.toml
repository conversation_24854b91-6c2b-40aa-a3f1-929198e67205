[project]
name = "ai-rag-application"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "elasticsearch==8.13.2",
    "google-generativeai>=0.5.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "aiohttp>=3.12.15",
    "agno>=1.7.9",
    "openai>=1.45.0",
    "google-genai>=1.29.0",
    "anthropic>=0.18.0",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]
