"""Test RAG entities and their properties."""

import pytest
from datetime import datetime
from typing import List

from src.domain.entities.rag_candidate import RAGCandidate
from src.domain.entities.rag_search import RAGSearchResult, RAGSearchMetadata


class TestRAGCandidate:
    """Test RAGCandidate entity."""
    
    def test_create_rag_candidate_with_required_fields(self):
        """Test creating RAGCandidate with all required fields."""
        candidate = RAGCandidate(
            category_id="test_category_id",
            similarity_score=0.85,
            category_data={"main_category": "交通設施", "description": "交通相關問題"},
            index_source="sub_categories",
            category_type="交通號誌",
            combined_text="交通號誌 標誌牌面 損壞"
        )
        
        assert candidate.category_id == "test_category_id"
        assert candidate.similarity_score == 0.85
        assert candidate.index_source == "sub_categories"
        assert candidate.category_type == "交通號誌"
        assert candidate.is_high_confidence is True
        assert candidate.confidence_level == "high"
    
    def test_confidence_levels(self):
        """Test confidence level properties."""
        # High confidence
        high_candidate = RAGCandidate(
            category_id="high",
            similarity_score=0.8,
            category_data={},
            index_source="sub_categories", 
            category_type="test",
            combined_text="test"
        )
        assert high_candidate.is_high_confidence is True
        assert high_candidate.confidence_level == "high"
        
        # Medium confidence
        medium_candidate = RAGCandidate(
            category_id="medium",
            similarity_score=0.65,
            category_data={},
            index_source="sub_categories",
            category_type="test", 
            combined_text="test"
        )
        assert medium_candidate.is_high_confidence is False
        assert medium_candidate.confidence_level == "medium"
        
        # Low confidence
        low_candidate = RAGCandidate(
            category_id="low",
            similarity_score=0.5,
            category_data={},
            index_source="sub_categories",
            category_type="test",
            combined_text="test"
        )
        assert low_candidate.is_high_confidence is False
        assert low_candidate.confidence_level == "low"
    
    def test_similarity_score_validation(self):
        """Test similarity score range validation."""
        with pytest.raises(ValueError):
            RAGCandidate(
                category_id="invalid",
                similarity_score=1.5,  # Invalid: > 1.0
                category_data={},
                index_source="sub_categories",
                category_type="test",
                combined_text="test"
            )
        
        with pytest.raises(ValueError):
            RAGCandidate(
                category_id="invalid",
                similarity_score=-0.1,  # Invalid: < 0.0
                category_data={},
                index_source="sub_categories", 
                category_type="test",
                combined_text="test"
            )


class TestRAGSearchResult:
    """Test RAGSearchResult entity."""
    
    def create_sample_metadata(self) -> RAGSearchMetadata:
        """Create sample metadata for testing."""
        return RAGSearchMetadata(
            embedding_generation_time_ms=100.0,
            elasticsearch_query_time_ms=250.0,
            total_search_time_ms=350.0,
            primary_threshold_used=0.75,
            total_candidates_found=3,
            sub_categories_searched=2,
            main_categories_searched=1
        )
    
    def create_sample_candidates(self) -> List[RAGCandidate]:
        """Create sample candidates for testing."""
        return [
            RAGCandidate(
                category_id="high_conf",
                similarity_score=0.85,
                category_data={"main_category": "交通設施"},
                index_source="sub_categories",
                category_type="交通號誌",
                combined_text="高信心候選"
            ),
            RAGCandidate(
                category_id="medium_conf",
                similarity_score=0.65,
                category_data={"main_category": "環境衛生"}, 
                index_source="main_categories",
                category_type="環境衛生",
                combined_text="中等信心候選"
            ),
            RAGCandidate(
                category_id="low_conf",
                similarity_score=0.45,
                category_data={"main_category": "其他"},
                index_source="main_categories",
                category_type="其他",
                combined_text="低信心候選"
            )
        ]
    
    def test_create_search_result(self):
        """Test creating RAGSearchResult."""
        metadata = self.create_sample_metadata()
        candidates = self.create_sample_candidates()
        
        result = RAGSearchResult(
            candidates=candidates,
            used_fallback_threshold=False,
            search_metadata=metadata
        )
        
        assert len(result.candidates) == 3
        assert result.used_fallback_threshold is False
        assert result.has_high_confidence_matches is True
        assert result.best_candidate.category_id == "high_conf"
    
    def test_confidence_filtering_properties(self):
        """Test confidence-based filtering properties."""
        candidates = self.create_sample_candidates()
        metadata = self.create_sample_metadata()
        
        result = RAGSearchResult(
            candidates=candidates,
            search_metadata=metadata
        )
        
        high_confidence = result.high_confidence_candidates
        assert len(high_confidence) == 1
        assert high_confidence[0].category_id == "high_conf"
        
        medium_confidence = result.medium_confidence_candidates
        assert len(medium_confidence) == 1
        assert medium_confidence[0].category_id == "medium_conf"
    
    def test_fallback_threshold_behavior(self):
        """Test fallback threshold behavior."""
        metadata = self.create_sample_metadata()
        
        # Test with fallback used
        result_with_fallback = RAGSearchResult(
            candidates=[],
            used_fallback_threshold=True,
            search_metadata=metadata
        )
        
        assert result_with_fallback.has_high_confidence_matches is False
        assert result_with_fallback.best_candidate is None