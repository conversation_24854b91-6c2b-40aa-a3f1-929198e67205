"""
自定義異常類別，用於 Elasticsearch 操作的錯誤處理
"""
from typing import List, Dict, Any, Optional


class ElasticsearchOperationError(Exception):
    """Elasticsearch 操作基礎異常類別"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}

    def __str__(self):
        if self.details:
            return f"{self.message}. 詳細資訊: {self.details}"
        return self.message


class ElasticsearchBulkError(ElasticsearchOperationError):
    """批次索引操作失敗時的異常"""

    def __init__(self,
                 failed_items: List[Dict[str, Any]],
                 total_documents: int,
                 index_name: str,
                 operation_details: Optional[Dict[str, Any]] = None):
        self.failed_items = failed_items
        self.total_documents = total_documents
        self.index_name = index_name
        self.failed_count = len(failed_items)
        self.success_count = total_documents - self.failed_count

        message = (f"批次索引失敗: {self.failed_count}/{total_documents} 筆文件 "
                  f"在索引 '{index_name}' 中失敗。成功索引 {self.success_count} 筆文件。")

        # 分類錯誤類型
        error_categories = self._categorize_errors(failed_items)

        details = {
            "index_name": index_name,
            "total_documents": total_documents,
            "failed_count": self.failed_count,
            "success_count": self.success_count,
            "error_categories": error_categories,
            "failed_items": failed_items[:5],  # 只保留前5筆失敗項目
            "operation_details": operation_details or {},
            "suggestions": self._generate_suggestions(error_categories)
        }

        super().__init__(message, details)

    def _categorize_errors(self, failed_items: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """將錯誤分類"""
        categories = {}

        for item in failed_items:
            error_info = self._extract_error_info(item)
            error_type = error_info.get('type', 'unknown')

            if error_type not in categories:
                categories[error_type] = []
            categories[error_type].append(error_info)

        return categories

    def _extract_error_info(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """從失敗項目中提取錯誤資訊"""
        if 'index' in item:
            error = item['index'].get('error', {})
            return {
                'type': error.get('type', 'unknown'),
                'reason': error.get('reason', '未提供原因'),
                'status': item['index'].get('status'),
                'document_id': item['index'].get('_id'),
                'caused_by': error.get('caused_by', {})
            }
        elif 'create' in item:
            error = item['create'].get('error', {})
            return {
                'type': error.get('type', 'unknown'),
                'reason': error.get('reason', '未提供原因'),
                'status': item['create'].get('status'),
                'document_id': item['create'].get('_id'),
                'caused_by': error.get('caused_by', {})
            }
        return {'type': 'unknown', 'reason': '無法提取錯誤資訊'}

    def _generate_suggestions(self, error_categories: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """根據錯誤類型生成建議"""
        suggestions = []

        for error_type, errors in error_categories.items():
            if error_type == 'mapper_parsing_exception':
                suggestions.extend([
                    "檢查文件結構是否符合索引映射",
                    "確認資料類型是否正確",
                    "考慮更新索引映射或轉換資料格式"
                ])
            elif error_type == 'version_conflict_engine_exception':
                suggestions.extend([
                    "處理文件版本衝突",
                    "使用適當的版本控制策略",
                    "考慮使用 upsert 操作"
                ])
            elif error_type == 'index_not_found_exception':
                suggestions.extend([
                    "確保目標索引存在",
                    "先建立索引並設定正確的映射",
                    "檢查索引名稱是否正確"
                ])
            elif error_type == 'circuit_breaking_exception':
                suggestions.extend([
                    "減少批次大小以避免記憶體壓力",
                    "增加 Elasticsearch 記憶體限制",
                    "實施重試機制"
                ])
            elif error_type == 'validation_exception':
                suggestions.extend([
                    "檢查文件欄位是否符合映射定義",
                    "確認必要欄位都有提供",
                    "檢查資料格式是否正確"
                ])

        if not suggestions:
            suggestions = [
                "查看 Elasticsearch 日誌獲取更多資訊",
                "驗證文件格式和結構",
                "嘗試較小的批次大小",
                "檢查 Elasticsearch 叢集健康狀態"
            ]

        return list(set(suggestions))  # 移除重複項

    def get_failed_documents(self) -> List[Dict[str, Any]]:
        """取得失敗文件的詳細資訊"""
        return [
            {
                'document_id': self._extract_error_info(item).get('document_id'),
                'error_type': self._extract_error_info(item).get('type'),
                'error_reason': self._extract_error_info(item).get('reason'),
                'status': self._extract_error_info(item).get('status')
            }
            for item in self.failed_items
        ]


class ElasticsearchConnectionError(ElasticsearchOperationError):
    """Elasticsearch 連線錯誤"""

    def __init__(self, host: str, original_error: Optional[Exception] = None):
        message = f"無法連線到 Elasticsearch: {host}"
        details = {
            "host": host,
            "original_error": str(original_error) if original_error else None,
            "suggestions": [
                "檢查 Elasticsearch 是否正在執行",
                "驗證主機和連接埠配置",
                "檢查網路連線",
                "驗證 Elasticsearch 認證設定"
            ]
        }
        super().__init__(message, details)