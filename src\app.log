2025-08-06 12:02:45,846 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:02:45,848 - __main__ - INFO - 環境: local
2025-08-06 12:02:46,204 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:02:46,232 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.000s]
2025-08-06 12:02:46,237 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.016s]
2025-08-06 12:02:46,666 - __main__ - ERROR - 應用程式發生錯誤: ResourceExhausted: 429 Quota exceeded for aiplatform.googleapis.com/embed_content_input_tokens_per_minute_per_base_model with base model: gemini-embedding. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai.
Traceback (most recent call last):
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\main.py", line 48, in main
    await use_case.execute(json_file_path)
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\application\use_cases\process_categories.py", line 41, in execute
    main_embeddings = await self.embedder.generate_embeddings_batch(main_texts[:1])
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\infrastructure\embedders\gemini_embedder.py", line 53, in generate_embeddings_batch
    embeddings = await asyncio.gather(*tasks)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\infrastructure\embedders\gemini_embedder.py", line 36, in generate_embedding
    embedding = await loop.run_in_executor(None, _generate)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.12.9-windows-x86_64-none\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\infrastructure\embedders\gemini_embedder.py", line 29, in _generate
    result = genai.embed_content(
             ^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\generativeai\embedding.py", line 213, in embed_content
    embedding_response = client.embed_content(
                         ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\ai\generativelanguage_v1beta\services\generative_service\client.py", line 1263, in embed_content
    response = rpc(
               ^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\gapic_v1\method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\retry\retry_unary.py", line 294, in retry_wrapped_func
    return retry_target(
           ^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\retry\retry_unary.py", line 156, in retry_target
    next_sleep = _retry_error_helper(
                 ^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\retry\retry_base.py", line 214, in _retry_error_helper
    raise final_exc from source_exc
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\retry\retry_unary.py", line 147, in retry_target
    result = target()
             ^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\timeout.py", line 130, in func_with_timeout
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\.venv\Lib\site-packages\google\api_core\grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.ResourceExhausted: 429 Quota exceeded for aiplatform.googleapis.com/embed_content_input_tokens_per_minute_per_base_model with base model: gemini-embedding. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai.
2025-08-06 12:02:46,679 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 12:06:40,456 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:06:40,459 - __main__ - INFO - 環境: local
2025-08-06 12:06:40,877 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:06:41,047 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.141s]
2025-08-06 12:06:41,051 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.015s]
2025-08-06 12:06:42,317 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 21 筆主案類到索引 main_categories
2025-08-06 12:06:42,341 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.016s]
2025-08-06 12:06:42,342 - src.infrastructure.repositories.elasticsearch_repository - ERROR - 批次索引主案類時發生錯誤: 21/21 筆失敗
2025-08-06 12:06:42,342 - src.infrastructure.repositories.elasticsearch_repository - ERROR - 批次索引主案類時發生未預期的錯誤: ElasticsearchBulkError: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。. 詳細資訊: {'index_name': 'main_categories', 'total_documents': 21, 'failed_count': 21, 'success_count': 0, 'error_categories': {'document_parsing_exception': [{'type': 'document_parsing_exception', 'reason': "[1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:06:40.878202'", 'status': 400, 'document_id': '交通號誌、標誌、標線及大眾運輸', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.878202] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:06:40.880129'", 'status': 400, 'document_id': '路霸排除', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.880129] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:06:40.880129'", 'status': 400, 'document_id': '噪音、污染及環境維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.880129] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:06:40.881765'", 'status': 400, 'document_id': '其他類別', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.881765] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:06:40.881765'", 'status': 400, 'document_id': '警政及交通裁罰業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.881765] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:637] failed to parse field [created_at] of type [date] in document with id '建築管理'. Preview of field's value: '2025-08-06T12:06:40.882767'", 'status': 400, 'document_id': '建築管理', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.882767] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:669] failed to parse field [created_at] of type [date] in document with id '道路、水溝維護'. Preview of field's value: '2025-08-06T12:06:40.882767'", 'status': 400, 'document_id': '道路、水溝維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.882767] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:568] failed to parse field [created_at] of type [date] in document with id '路燈、路樹及公園管理維護'. Preview of field's value: '2025-08-06T12:06:40.883769'", 'status': 400, 'document_id': '路燈、路樹及公園管理維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.883769] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:511] failed to parse field [created_at] of type [date] in document with id '衛生行政'. Preview of field's value: '2025-08-06T12:06:40.884384'", 'status': 400, 'document_id': '衛生行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.884384] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:652] failed to parse field [created_at] of type [date] in document with id '教育及體育'. Preview of field's value: '2025-08-06T12:06:40.884384'", 'status': 400, 'document_id': '教育及體育', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.884384] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:543] failed to parse field [created_at] of type [date] in document with id '勞動行政'. Preview of field's value: '2025-08-06T12:06:40.885495'", 'status': 400, 'document_id': '勞動行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.885495] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:625] failed to parse field [created_at] of type [date] in document with id '工商、經濟及稅務'. Preview of field's value: '2025-08-06T12:06:40.886493'", 'status': 400, 'document_id': '工商、經濟及稅務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.886493] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:1001] failed to parse field [created_at] of type [date] in document with id '社會救助及社會福利'. Preview of field's value: '2025-08-06T12:06:40.886493'", 'status': 400, 'document_id': '社會救助及社會福利', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.886493] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:490] failed to parse field [created_at] of type [date] in document with id '地政服務'. Preview of field's value: '2025-08-06T12:06:40.888129'", 'status': 400, 'document_id': '地政服務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.888129] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:504] failed to parse field [created_at] of type [date] in document with id '消防行政'. Preview of field's value: '2025-08-06T12:06:40.889085'", 'status': 400, 'document_id': '消防行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.889085] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:631] failed to parse field [created_at] of type [date] in document with id '感謝函、服務品質及網站、APP管理問題'. Preview of field's value: '2025-08-06T12:06:40.889085'", 'status': 400, 'document_id': '感謝函、服務品質及網站、APP管理問題', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.889085] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:487] failed to parse field [created_at] of type [date] in document with id '動物收容、保護及捕捉'. Preview of field's value: '2025-08-06T12:06:40.889640'", 'status': 400, 'document_id': '動物收容、保護及捕捉', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.889640] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:538] failed to parse field [created_at] of type [date] in document with id '文化藝術及圖書管理'. Preview of field's value: '2025-08-06T12:06:40.889640'", 'status': 400, 'document_id': '文化藝術及圖書管理', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.889640] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:427] failed to parse field [created_at] of type [date] in document with id '民政業務'. Preview of field's value: '2025-08-06T12:06:40.890640'", 'status': 400, 'document_id': '民政業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.890640] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:405] failed to parse field [created_at] of type [date] in document with id '政風行政'. Preview of field's value: '2025-08-06T12:06:40.890640'", 'status': 400, 'document_id': '政風行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.890640] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:586] failed to parse field [created_at] of type [date] in document with id '市民卡業務'. Preview of field's value: '2025-08-06T12:06:40.890640'", 'status': 400, 'document_id': '市民卡業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.890640] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}]}, 'failed_items': [{'index': {'_index': 'main_categories', '_id': '交通號誌、標誌、標線及大眾運輸', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:06:40.878202'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.878202] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '路霸排除', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:06:40.880129'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.880129] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '噪音、污染及環境維護', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:06:40.880129'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.880129] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '其他類別', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:06:40.881765'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.881765] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '警政及交通裁罰業務', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:06:40.881765'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:06:40.881765] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}], 'operation_details': {'operation': 'save_main_categories_batch', 'document_type': 'MainCategory'}, 'suggestions': ['檢查 Elasticsearch 叢集健康狀態', '嘗試較小的批次大小', '查看 Elasticsearch 日誌獲取更多資訊', '驗證文件格式和結構']}
2025-08-06 12:06:42,345 - src.application.use_cases.process_categories - ERROR - 儲存主案類時發生錯誤: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。
2025-08-06 12:06:42,346 - __main__ - ERROR - 批次索引錯誤: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。
2025-08-06 12:06:42,346 - __main__ - ERROR - 失敗文件 - ID: 交通號誌、標誌、標線及大眾運輸, 錯誤: document_parsing_exception - [1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:06:40.878202'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 路霸排除, 錯誤: document_parsing_exception - [1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:06:40.880129'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 噪音、污染及環境維護, 錯誤: document_parsing_exception - [1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:06:40.880129'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 其他類別, 錯誤: document_parsing_exception - [1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:06:40.881765'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 警政及交通裁罰業務, 錯誤: document_parsing_exception - [1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:06:40.881765'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 建築管理, 錯誤: document_parsing_exception - [1:637] failed to parse field [created_at] of type [date] in document with id '建築管理'. Preview of field's value: '2025-08-06T12:06:40.882767'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 道路、水溝維護, 錯誤: document_parsing_exception - [1:669] failed to parse field [created_at] of type [date] in document with id '道路、水溝維護'. Preview of field's value: '2025-08-06T12:06:40.882767'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 路燈、路樹及公園管理維護, 錯誤: document_parsing_exception - [1:568] failed to parse field [created_at] of type [date] in document with id '路燈、路樹及公園管理維護'. Preview of field's value: '2025-08-06T12:06:40.883769'
2025-08-06 12:06:42,347 - __main__ - ERROR - 失敗文件 - ID: 衛生行政, 錯誤: document_parsing_exception - [1:511] failed to parse field [created_at] of type [date] in document with id '衛生行政'. Preview of field's value: '2025-08-06T12:06:40.884384'
2025-08-06 12:06:42,348 - __main__ - ERROR - 失敗文件 - ID: 教育及體育, 錯誤: document_parsing_exception - [1:652] failed to parse field [created_at] of type [date] in document with id '教育及體育'. Preview of field's value: '2025-08-06T12:06:40.884384'
2025-08-06 12:06:42,348 - __main__ - ERROR - 失敗文件 - ID: 勞動行政, 錯誤: document_parsing_exception - [1:543] failed to parse field [created_at] of type [date] in document with id '勞動行政'. Preview of field's value: '2025-08-06T12:06:40.885495'
2025-08-06 12:06:42,348 - __main__ - ERROR - 失敗文件 - ID: 工商、經濟及稅務, 錯誤: document_parsing_exception - [1:625] failed to parse field [created_at] of type [date] in document with id '工商、經濟及稅務'. Preview of field's value: '2025-08-06T12:06:40.886493'
2025-08-06 12:06:42,348 - __main__ - ERROR - 失敗文件 - ID: 社會救助及社會福利, 錯誤: document_parsing_exception - [1:1001] failed to parse field [created_at] of type [date] in document with id '社會救助及社會福利'. Preview of field's value: '2025-08-06T12:06:40.886493'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 地政服務, 錯誤: document_parsing_exception - [1:490] failed to parse field [created_at] of type [date] in document with id '地政服務'. Preview of field's value: '2025-08-06T12:06:40.888129'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 消防行政, 錯誤: document_parsing_exception - [1:504] failed to parse field [created_at] of type [date] in document with id '消防行政'. Preview of field's value: '2025-08-06T12:06:40.889085'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 感謝函、服務品質及網站、APP管理問題, 錯誤: document_parsing_exception - [1:631] failed to parse field [created_at] of type [date] in document with id '感謝函、服務品質及網站、APP管理問題'. Preview of field's value: '2025-08-06T12:06:40.889085'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 動物收容、保護及捕捉, 錯誤: document_parsing_exception - [1:487] failed to parse field [created_at] of type [date] in document with id '動物收容、保護及捕捉'. Preview of field's value: '2025-08-06T12:06:40.889640'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 文化藝術及圖書管理, 錯誤: document_parsing_exception - [1:538] failed to parse field [created_at] of type [date] in document with id '文化藝術及圖書管理'. Preview of field's value: '2025-08-06T12:06:40.889640'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 民政業務, 錯誤: document_parsing_exception - [1:427] failed to parse field [created_at] of type [date] in document with id '民政業務'. Preview of field's value: '2025-08-06T12:06:40.890640'
2025-08-06 12:06:42,349 - __main__ - ERROR - 失敗文件 - ID: 政風行政, 錯誤: document_parsing_exception - [1:405] failed to parse field [created_at] of type [date] in document with id '政風行政'. Preview of field's value: '2025-08-06T12:06:40.890640'
2025-08-06 12:06:42,350 - __main__ - ERROR - 失敗文件 - ID: 市民卡業務, 錯誤: document_parsing_exception - [1:586] failed to parse field [created_at] of type [date] in document with id '市民卡業務'. Preview of field's value: '2025-08-06T12:06:40.890640'
2025-08-06 12:06:42,351 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 12:16:43,791 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:16:43,793 - __main__ - INFO - 環境: local
2025-08-06 12:16:44,105 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:16:44,133 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.016s]
2025-08-06 12:16:44,138 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.000s]
2025-08-06 12:16:45,432 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 21 筆主案類到索引 main_categories
2025-08-06 12:16:45,464 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.032s]
2025-08-06 12:16:45,464 - src.infrastructure.repositories.elasticsearch_repository - ERROR - 批次索引主案類時發生錯誤: 21/21 筆失敗
2025-08-06 12:16:45,465 - src.infrastructure.repositories.elasticsearch_repository - ERROR - 批次索引主案類時發生未預期的錯誤: ElasticsearchBulkError: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。. 詳細資訊: {'index_name': 'main_categories', 'total_documents': 21, 'failed_count': 21, 'success_count': 0, 'error_categories': {'document_parsing_exception': [{'type': 'document_parsing_exception', 'reason': "[1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:16:44.106937'", 'status': 400, 'document_id': '交通號誌、標誌、標線及大眾運輸', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.106937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:16:44.108952'", 'status': 400, 'document_id': '路霸排除', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.108952] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:16:44.108952'", 'status': 400, 'document_id': '噪音、污染及環境維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.108952] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:16:44.109937'", 'status': 400, 'document_id': '其他類別', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.109937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:16:44.109937'", 'status': 400, 'document_id': '警政及交通裁罰業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.109937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:637] failed to parse field [created_at] of type [date] in document with id '建築管理'. Preview of field's value: '2025-08-06T12:16:44.110954'", 'status': 400, 'document_id': '建築管理', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.110954] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:669] failed to parse field [created_at] of type [date] in document with id '道路、水溝維護'. Preview of field's value: '2025-08-06T12:16:44.111937'", 'status': 400, 'document_id': '道路、水溝維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.111937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:568] failed to parse field [created_at] of type [date] in document with id '路燈、路樹及公園管理維護'. Preview of field's value: '2025-08-06T12:16:44.111937'", 'status': 400, 'document_id': '路燈、路樹及公園管理維護', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.111937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:511] failed to parse field [created_at] of type [date] in document with id '衛生行政'. Preview of field's value: '2025-08-06T12:16:44.112951'", 'status': 400, 'document_id': '衛生行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.112951] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:652] failed to parse field [created_at] of type [date] in document with id '教育及體育'. Preview of field's value: '2025-08-06T12:16:44.112951'", 'status': 400, 'document_id': '教育及體育', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.112951] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:543] failed to parse field [created_at] of type [date] in document with id '勞動行政'. Preview of field's value: '2025-08-06T12:16:44.113938'", 'status': 400, 'document_id': '勞動行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.113938] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:625] failed to parse field [created_at] of type [date] in document with id '工商、經濟及稅務'. Preview of field's value: '2025-08-06T12:16:44.114440'", 'status': 400, 'document_id': '工商、經濟及稅務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.114440] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:1001] failed to parse field [created_at] of type [date] in document with id '社會救助及社會福利'. Preview of field's value: '2025-08-06T12:16:44.114440'", 'status': 400, 'document_id': '社會救助及社會福利', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.114440] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:490] failed to parse field [created_at] of type [date] in document with id '地政服務'. Preview of field's value: '2025-08-06T12:16:44.115443'", 'status': 400, 'document_id': '地政服務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.115443] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:504] failed to parse field [created_at] of type [date] in document with id '消防行政'. Preview of field's value: '2025-08-06T12:16:44.116444'", 'status': 400, 'document_id': '消防行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.116444] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:631] failed to parse field [created_at] of type [date] in document with id '感謝函、服務品質及網站、APP管理問題'. Preview of field's value: '2025-08-06T12:16:44.116444'", 'status': 400, 'document_id': '感謝函、服務品質及網站、APP管理問題', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.116444] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:487] failed to parse field [created_at] of type [date] in document with id '動物收容、保護及捕捉'. Preview of field's value: '2025-08-06T12:16:44.116444'", 'status': 400, 'document_id': '動物收容、保護及捕捉', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.116444] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:538] failed to parse field [created_at] of type [date] in document with id '文化藝術及圖書管理'. Preview of field's value: '2025-08-06T12:16:44.117443'", 'status': 400, 'document_id': '文化藝術及圖書管理', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.117443] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:427] failed to parse field [created_at] of type [date] in document with id '民政業務'. Preview of field's value: '2025-08-06T12:16:44.117443'", 'status': 400, 'document_id': '民政業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.117443] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:405] failed to parse field [created_at] of type [date] in document with id '政風行政'. Preview of field's value: '2025-08-06T12:16:44.117443'", 'status': 400, 'document_id': '政風行政', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.117443] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}, {'type': 'document_parsing_exception', 'reason': "[1:586] failed to parse field [created_at] of type [date] in document with id '市民卡業務'. Preview of field's value: '2025-08-06T12:16:44.118443'", 'status': 400, 'document_id': '市民卡業務', 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.118443] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}]}, 'failed_items': [{'index': {'_index': 'main_categories', '_id': '交通號誌、標誌、標線及大眾運輸', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:16:44.106937'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.106937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '路霸排除', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:16:44.108952'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.108952] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '噪音、污染及環境維護', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:16:44.108952'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.108952] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '其他類別', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:16:44.109937'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.109937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}, {'index': {'_index': 'main_categories', '_id': '警政及交通裁罰業務', 'status': 400, 'error': {'type': 'document_parsing_exception', 'reason': "[1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:16:44.109937'", 'caused_by': {'type': 'illegal_argument_exception', 'reason': 'failed to parse date field [2025-08-06T12:16:44.109937] with format [yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis]', 'caused_by': {'type': 'date_time_parse_exception', 'reason': 'Failed to parse with all enclosed parsers'}}}}}], 'operation_details': {'operation': 'save_main_categories_batch', 'document_type': 'MainCategory'}, 'suggestions': ['嘗試較小的批次大小', '檢查 Elasticsearch 叢集健康狀態', '查看 Elasticsearch 日誌獲取更多資訊', '驗證文件格式和結構']}
2025-08-06 12:16:45,466 - src.application.use_cases.process_categories - ERROR - 儲存主案類時發生錯誤: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。
2025-08-06 12:16:45,467 - __main__ - ERROR - 批次索引錯誤: 批次索引失敗: 21/21 筆文件 在索引 'main_categories' 中失敗。成功索引 0 筆文件。
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 交通號誌、標誌、標線及大眾運輸, 錯誤: document_parsing_exception - [1:40186] failed to parse field [created_at] of type [date] in document with id '交通號誌、標誌、標線及大眾運輸'. Preview of field's value: '2025-08-06T12:16:44.106937'
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 路霸排除, 錯誤: document_parsing_exception - [1:520] failed to parse field [created_at] of type [date] in document with id '路霸排除'. Preview of field's value: '2025-08-06T12:16:44.108952'
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 噪音、污染及環境維護, 錯誤: document_parsing_exception - [1:769] failed to parse field [created_at] of type [date] in document with id '噪音、污染及環境維護'. Preview of field's value: '2025-08-06T12:16:44.108952'
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 其他類別, 錯誤: document_parsing_exception - [1:420] failed to parse field [created_at] of type [date] in document with id '其他類別'. Preview of field's value: '2025-08-06T12:16:44.109937'
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 警政及交通裁罰業務, 錯誤: document_parsing_exception - [1:656] failed to parse field [created_at] of type [date] in document with id '警政及交通裁罰業務'. Preview of field's value: '2025-08-06T12:16:44.109937'
2025-08-06 12:16:45,467 - __main__ - ERROR - 失敗文件 - ID: 建築管理, 錯誤: document_parsing_exception - [1:637] failed to parse field [created_at] of type [date] in document with id '建築管理'. Preview of field's value: '2025-08-06T12:16:44.110954'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 道路、水溝維護, 錯誤: document_parsing_exception - [1:669] failed to parse field [created_at] of type [date] in document with id '道路、水溝維護'. Preview of field's value: '2025-08-06T12:16:44.111937'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 路燈、路樹及公園管理維護, 錯誤: document_parsing_exception - [1:568] failed to parse field [created_at] of type [date] in document with id '路燈、路樹及公園管理維護'. Preview of field's value: '2025-08-06T12:16:44.111937'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 衛生行政, 錯誤: document_parsing_exception - [1:511] failed to parse field [created_at] of type [date] in document with id '衛生行政'. Preview of field's value: '2025-08-06T12:16:44.112951'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 教育及體育, 錯誤: document_parsing_exception - [1:652] failed to parse field [created_at] of type [date] in document with id '教育及體育'. Preview of field's value: '2025-08-06T12:16:44.112951'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 勞動行政, 錯誤: document_parsing_exception - [1:543] failed to parse field [created_at] of type [date] in document with id '勞動行政'. Preview of field's value: '2025-08-06T12:16:44.113938'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 工商、經濟及稅務, 錯誤: document_parsing_exception - [1:625] failed to parse field [created_at] of type [date] in document with id '工商、經濟及稅務'. Preview of field's value: '2025-08-06T12:16:44.114440'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 社會救助及社會福利, 錯誤: document_parsing_exception - [1:1001] failed to parse field [created_at] of type [date] in document with id '社會救助及社會福利'. Preview of field's value: '2025-08-06T12:16:44.114440'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 地政服務, 錯誤: document_parsing_exception - [1:490] failed to parse field [created_at] of type [date] in document with id '地政服務'. Preview of field's value: '2025-08-06T12:16:44.115443'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 消防行政, 錯誤: document_parsing_exception - [1:504] failed to parse field [created_at] of type [date] in document with id '消防行政'. Preview of field's value: '2025-08-06T12:16:44.116444'
2025-08-06 12:16:45,468 - __main__ - ERROR - 失敗文件 - ID: 感謝函、服務品質及網站、APP管理問題, 錯誤: document_parsing_exception - [1:631] failed to parse field [created_at] of type [date] in document with id '感謝函、服務品質及網站、APP管理問題'. Preview of field's value: '2025-08-06T12:16:44.116444'
2025-08-06 12:16:45,469 - __main__ - ERROR - 失敗文件 - ID: 動物收容、保護及捕捉, 錯誤: document_parsing_exception - [1:487] failed to parse field [created_at] of type [date] in document with id '動物收容、保護及捕捉'. Preview of field's value: '2025-08-06T12:16:44.116444'
2025-08-06 12:16:45,469 - __main__ - ERROR - 失敗文件 - ID: 文化藝術及圖書管理, 錯誤: document_parsing_exception - [1:538] failed to parse field [created_at] of type [date] in document with id '文化藝術及圖書管理'. Preview of field's value: '2025-08-06T12:16:44.117443'
2025-08-06 12:16:45,469 - __main__ - ERROR - 失敗文件 - ID: 民政業務, 錯誤: document_parsing_exception - [1:427] failed to parse field [created_at] of type [date] in document with id '民政業務'. Preview of field's value: '2025-08-06T12:16:44.117443'
2025-08-06 12:16:45,469 - __main__ - ERROR - 失敗文件 - ID: 政風行政, 錯誤: document_parsing_exception - [1:405] failed to parse field [created_at] of type [date] in document with id '政風行政'. Preview of field's value: '2025-08-06T12:16:44.117443'
2025-08-06 12:16:45,469 - __main__ - ERROR - 失敗文件 - ID: 市民卡業務, 錯誤: document_parsing_exception - [1:586] failed to parse field [created_at] of type [date] in document with id '市民卡業務'. Preview of field's value: '2025-08-06T12:16:44.118443'
2025-08-06 12:16:45,469 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 12:22:21,999 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:22:22,002 - __main__ - INFO - 環境: local
2025-08-06 12:22:22,289 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:22:22,314 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.016s]
2025-08-06 12:22:22,318 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.000s]
2025-08-06 12:22:22,319 - __main__ - ERROR - 應用程式發生錯誤: AssertionError: 
Traceback (most recent call last):
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\main.py", line 48, in main
    await use_case.execute(json_file_path)
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\application\use_cases\process_categories.py", line 39, in execute
    assert False
           ^^^^^
AssertionError
2025-08-06 12:22:22,319 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 12:40:47,495 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:40:47,496 - __main__ - INFO - 環境: local
2025-08-06 12:40:47,790 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:40:47,814 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.016s]
2025-08-06 12:40:47,816 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.000s]
2025-08-06 12:40:47,818 - __main__ - ERROR - 應用程式發生錯誤: AssertionError: 
Traceback (most recent call last):
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\main.py", line 48, in main
    await use_case.execute(json_file_path)
  File "D:\Users\leamo\work\projects\1999\ai-rag-application\src\application\use_cases\process_categories.py", line 39, in execute
    assert False
           ^^^^^
AssertionError
2025-08-06 12:40:47,820 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 12:41:24,782 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 12:41:24,783 - __main__ - INFO - 環境: local
2025-08-06 12:41:25,077 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 12:41:25,098 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:200 duration:0.015s]
2025-08-06 12:41:25,102 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:200 duration:0.000s]
2025-08-06 12:41:25,943 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 1 筆主案類到索引 main_categories
2025-08-06 12:41:26,161 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.219s]
2025-08-06 12:41:26,161 - src.infrastructure.repositories.elasticsearch_repository - INFO - 成功批次索引 1 筆主案類
2025-08-06 12:41:26,161 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 1 筆子案類到索引 sub_categories
2025-08-06 12:41:26,202 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.031s]
2025-08-06 12:41:26,203 - src.infrastructure.repositories.elasticsearch_repository - INFO - 成功批次索引 1 筆子案類
2025-08-06 12:41:26,204 - __main__ - INFO - 應用程式執行成功完成
2025-08-06 12:41:26,204 - __main__ - INFO - 已關閉 Elasticsearch 連線
2025-08-06 13:09:54,594 - __main__ - INFO - 開始執行 AI RAG 應用程式
2025-08-06 13:09:54,594 - __main__ - INFO - 環境: local
2025-08-06 13:09:55,011 - __main__ - INFO - 開始處理檔案: data/categories_def.json
2025-08-06 13:09:55,037 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/main_categories [status:404 duration:0.016s]
2025-08-06 13:09:57,019 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/main_categories [status:200 duration:1.984s]
2025-08-06 13:09:57,022 - elastic_transport.transport - INFO - HEAD https://127.0.0.1:9200/sub_categories [status:404 duration:0.000s]
2025-08-06 13:09:59,995 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/sub_categories [status:200 duration:2.969s]
2025-08-06 13:10:01,306 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 1 筆主案類到索引 main_categories
2025-08-06 13:10:01,402 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.078s]
2025-08-06 13:10:01,402 - src.infrastructure.repositories.elasticsearch_repository - INFO - 成功批次索引 1 筆主案類
2025-08-06 13:10:01,403 - src.infrastructure.repositories.elasticsearch_repository - INFO - 開始批次索引 1 筆子案類到索引 sub_categories
2025-08-06 13:10:01,438 - elastic_transport.transport - INFO - PUT https://127.0.0.1:9200/_bulk [status:200 duration:0.047s]
2025-08-06 13:10:01,439 - src.infrastructure.repositories.elasticsearch_repository - INFO - 成功批次索引 1 筆子案類
2025-08-06 13:10:01,439 - __main__ - INFO - 應用程式執行成功完成
2025-08-06 13:10:01,440 - __main__ - INFO - 已關閉 Elasticsearch 連線
