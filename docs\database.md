---
title: Database Schema
description: "Documents the Elasticsearch index schemas, mappings, and data storage architecture for the Taiwan government complaint RAG system."
inclusion: always
---

# Database Schema

## Database Configuration

### Elasticsearch 8.13.2

**Version:** 8.13.2
**Connection:** Configurable via ELASTICSEARCH_URL (default: http://127.0.0.1:9200)
**Authentication:** Optional username/password authentication
**SSL/TLS:** Environment-specific CA certificates in `src/certs/es/{environment}/ca/`

### Environment Configuration

-   **local**: Development with local Elasticsearch instance
-   **dev**: Development environment with shared Elasticsearch cluster
-   **prod**: Production environment with full SSL/TLS security

## Elasticsearch Index Schemas

### Main Categories Index

**Index Name:** `main_categories` (configurable via MAIN_CATEGORIES_INDEX)

**Mappings:**

```json
{
	"mappings": {
		"properties": {
			"main_category": {
				"type": "keyword",
				"index": true
			},
			"description": {
				"type": "text",
				"analyzer": "ik_max_word",
				"fields": {
					"keyword": {
						"type": "keyword",
						"ignore_above": 256
					}
				}
			},
			"combined_text": {
				"type": "text",
				"analyzer": "ik_max_word"
			},
			"sub_categories": {
				"type": "keyword"
			},
			"embedding": {
				"type": "dense_vector",
				"dims": 3072,
				"index": true,
				"similarity": "cosine"
			},
			"created_at": {
				"type": "date",
				"format": "yyyy/MM/dd HH:mm:ss"
			}
		}
	},
	"settings": {
		"analysis": {
			"analyzer": {
				"ik_max_word": {
					"type": "ik_max_word"
				}
			}
		}
	}
}
```

**Document Structure:**

-   `main_category`: Primary complaint category name (keyword)
-   `description`: Detailed description of the main category (text with Chinese analysis)
-   `combined_text`: Structured text for embedding ("主案類: {name}\\n 描述: {description}")
-   `sub_categories`: Array of related subcategory names (keyword array)
-   `embedding`: 3072-dimensional vector from Gemini embedding-001 (dense_vector)
-   `created_at`: Document creation timestamp (date)

### Sub Categories Index

**Index Name:** `sub_categories` (configurable via SUB_CATEGORIES_INDEX)

**Mappings:**

```json
{
	"mappings": {
		"properties": {
			"main_category": {
				"type": "keyword",
				"index": true
			},
			"sub_category": {
				"type": "keyword",
				"index": true
			},
			"sub_description": {
				"type": "text",
				"analyzer": "ik_max_word",
				"fields": {
					"keyword": {
						"type": "keyword",
						"ignore_above": 256
					}
				}
			},
			"main_description": {
				"type": "text",
				"analyzer": "ik_max_word"
			},
			"combined_text": {
				"type": "text",
				"analyzer": "ik_max_word"
			},
			"keywords": {
				"type": "keyword"
			},
			"embedding": {
				"type": "dense_vector",
				"dims": 3072,
				"index": true,
				"similarity": "cosine"
			},
			"created_at": {
				"type": "date",
				"format": "yyyy/MM/dd HH:mm:ss"
			}
		}
	},
	"settings": {
		"analysis": {
			"analyzer": {
				"ik_max_word": {
					"type": "ik_max_word"
				}
			}
		}
	}
}
```

**Document Structure:**

-   `main_category`: Parent main category name (keyword)
-   `sub_category`: Specific subcategory name (keyword)
-   `sub_description`: Detailed description of the subcategory (text with Chinese analysis)
-   `main_description`: Description of the parent main category (text)
-   `combined_text`: Structured text for embedding ("子案類: {name}\\n 描述: {description}\\n 所屬主案類: {parent}")
-   `keywords`: Extracted keywords using regex patterns (keyword array)
-   `embedding`: 3072-dimensional vector from Gemini embedding-001 (dense_vector)
-   `created_at`: Document creation timestamp (date)

## Data Processing Pipeline

### Text Processing Flow

1. **JSON Data Loading**: Read Taiwan government complaint categories from `data/categories_def.json`
2. **Entity Creation**: Convert to domain entities (MainCategory, SubCategory) with Pydantic validation
3. **Text Structuring**: Create combined_text fields optimized for embedding generation
4. **Keyword Extraction**: Extract keywords from subcategory descriptions using regex patterns
5. **Batch Embedding**: Generate embeddings asynchronously using Google Gemini embedding-001
6. **Index Creation**: Ensure Elasticsearch indices exist with proper mappings
7. **Bulk Indexing**: Store documents in batches with comprehensive error handling

### Current Processing Characteristics

-   **Text Encoding**: UTF-8 throughout the pipeline with proper Chinese character support
-   **Error Recovery**: Comprehensive error handling with detailed failure reporting
-   **Idempotency**: Index creation is idempotent (can be run multiple times safely)

## Chinese Text Analysis

### IK Analyzer Configuration

The system uses the `ik_max_word` analyzer for Traditional Chinese text processing:

**Features:**

-   **Maximum Word Segmentation**: Provides comprehensive word segmentation for Chinese text
-   **Traditional Chinese Support**: Properly handles Traditional Chinese characters used in Taiwan
-   **Search Optimization**: Optimized for search scenarios with multiple segmentation strategies

**Applied To:**

-   `description` fields in both indices
-   `combined_text` fields used for contextual search
-   `main_description` and `sub_description` fields

### Vector Search Configuration

**Embedding Specifications:**

-   **Model**: Google Gemini embedding-001
-   **Dimensions**: 3072 (configurable via EMBEDDING_DIMS)
-   **Task Type**: RETRIEVAL_DOCUMENT (optimized for document retrieval)
-   **Similarity**: Cosine similarity for vector search

## Performance Considerations

### Indexing Performance

-   **Bulk Operations**: All document insertions use Elasticsearch bulk API
-   **Async Processing**: Non-blocking I/O for embedding generation and indexing
-   **Error Handling**: Comprehensive error categorization and recovery suggestions
-   **Connection Management**: Proper connection lifecycle with cleanup

### Search Performance

-   **Dense Vector Index**: Vectors are indexed for fast similarity search
-   **Keyword Access**: Category and subcategory fields are indexed as keywords for exact matching
-   **Hybrid Search Ready**: Text fields support both full-text and vector search capabilities

### Memory and Storage

-   **Vector Storage**: Each document stores a 3072-dimensional float vector (~12KB per embedding)
-   **Text Redundancy**: Combined text fields optimize for embedding quality vs. storage efficiency
-   **Index Optimization**: Proper field mappings minimize storage overhead

## Data Integrity

### Document Validation

-   **Pydantic Models**: Strict validation of data types and required fields
-   **Custom Serialization**: Proper datetime formatting for Elasticsearch compatibility
-   **UTF-8 Encoding**: Consistent encoding throughout the data pipeline

### Error Handling

-   **Bulk Operation Errors**: Detailed categorization of indexing failures
-   **Connection Errors**: Comprehensive connection diagnostics and suggestions
-   **Data Validation**: Pre-insertion validation prevents malformed documents

### Backup and Recovery

-   **Configuration**: Environment-specific settings enable proper backup strategies
-   **Logging**: Comprehensive logging supports debugging and audit trails
-   **Index Recreation**: Indices can be safely recreated from source data

This schema design supports the core RAG functionality while maintaining flexibility for future enhancements in the Taiwan government complaint processing system.
