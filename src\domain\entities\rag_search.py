from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from .rag_candidate import RAGCandidate


class RAGSearchMetadata(BaseModel):
    """Metadata about the similarity search operation."""
    
    search_timestamp: datetime = Field(default_factory=datetime.now)
    embedding_generation_time_ms: float = Field(..., description="Embedding生成時間（毫秒）")
    elasticsearch_query_time_ms: float = Field(..., description="Elasticsearch查詢時間（毫秒）")
    total_search_time_ms: float = Field(..., description="總搜索時間（毫秒）")
    primary_threshold_used: float = Field(..., description="使用的主要門檻值")
    fallback_threshold_used: Optional[float] = Field(None, description="使用的備用門檻值")
    total_candidates_found: int = Field(..., description="找到的候選數量")
    sub_categories_searched: int = Field(..., description="搜索的子類別數量")
    main_categories_searched: int = Field(..., description="搜索的主類別數量")


class RAGSearchResult(BaseModel):
    """Result container for similarity search operations."""
    
    candidates: List[RAGCandidate] = Field(default_factory=list, description="搜索候選列表")
    used_fallback_threshold: bool = Field(default=False, description="是否使用了備用門檻值")
    search_metadata: RAGSearchMetadata = Field(..., description="搜索元數據")
    
    @property
    def has_high_confidence_matches(self) -> bool:
        """True if any candidates found with primary threshold."""
        return not self.used_fallback_threshold and len(self.candidates) > 0
    
    @property
    def best_candidate(self) -> Optional[RAGCandidate]:
        """Returns the highest scoring candidate, if any."""
        return self.candidates[0] if self.candidates else None
    
    @property 
    def high_confidence_candidates(self) -> List[RAGCandidate]:
        """Returns only high confidence candidates (>= 0.75)."""
        return [c for c in self.candidates if c.is_high_confidence]
    
    @property
    def medium_confidence_candidates(self) -> List[RAGCandidate]:
        """Returns medium confidence candidates (0.6 <= score < 0.75)."""
        return [c for c in self.candidates if c.confidence_level == "medium"]