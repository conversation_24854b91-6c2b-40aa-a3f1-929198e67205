from typing import List, Optional
from pydantic import BaseModel, Field


class RAGCandidate(BaseModel):
    """
    Enhanced candidate with similarity search context.
    
    Represents a similarity search result candidate from RAG system
    with comprehensive metadata for classification decisions.
    """

    # Core identification fields
    category_id: str = Field(..., description="唯一識別碼")
    similarity_score: float = Field(..., description="向量相似性分數", ge=0.0, le=1.0)
    category_data: dict = Field(..., description="完整的類別資料")
    index_source: str = Field(..., description="資料來源索引：'sub_categories' 或 'main_categories'")
    
    # Enhanced fields for classification
    category_type: str = Field(..., description="類別類型，從category_data提取")
    keywords: List[str] = Field(default_factory=list, description="關鍵詞列表")
    combined_text: str = Field(..., description="用於embedding生成的組合文本")
    
    # Legacy fields for backward compatibility
    main_category: Optional[str] = Field(None, description="主案類名稱（向後兼容）")
    sub_category: Optional[str] = Field(None, description="子案類名稱（向後兼容）")
    sub_description: Optional[str] = Field(None, description="子案類描述（向後兼容）")
    main_description: Optional[str] = Field(None, description="主案類描述（向後兼容）")
    
    @property
    def is_high_confidence(self) -> bool:
        """True if similarity score meets high confidence threshold."""
        return self.similarity_score >= 0.75
    
    @property
    def confidence_level(self) -> str:
        """Returns 'high', 'medium', or 'low' based on similarity score."""
        if self.similarity_score >= 0.75:
            return "high"
        elif self.similarity_score >= 0.6:
            return "medium"
        else:
            return "low"

    class Config:
        """Pydantic configuration"""
        validate_assignment = True
        use_enum_values = True