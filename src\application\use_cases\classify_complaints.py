"""Main orchestrator use case for complete complaint classification workflow."""

import asyncio
import logging
from datetime import datetime
from typing import List

from ...domain.entities.classification_result import ClassificationResult
from ...domain.entities.complaint_input import ComplaintInput
from ...domain.entities.confidence_level import ConfidenceLevel
from ...domain.entities.intent_detection_request import IntentDetectionRequest
from ...domain.exceptions import (
    IntentDetectionError,
    RAGSearchError,
    LLMClassificationError,
    TimeoutError,
)
from ...domain.interfaces.classification_service_interface import ClassificationServiceInterface
from ...domain.interfaces.metrics_interface import MetricsInterface
from .detect_intents import DetectIntentsUseCase
from .similarity_search import SimilaritySearchUseCase

logger = logging.getLogger(__name__)


class ClassifyComplaintsUseCase:
    """
    Main orchestrator for complaint classification workflow.

    Coordinates intent detection, RAG similarity search, and LLM-based
    classification to produce final classification results.
    """

    def __init__(
        self,
        detect_intents_use_case: DetectIntentsUseCase,
        similarity_search_use_case: SimilaritySearchUseCase,
        classification_service: ClassificationServiceInterface,
        metrics: MetricsInterface,
        enable_llm_analysis: bool = True,
    ):
        self._detect_intents = detect_intents_use_case
        self._similarity_search = similarity_search_use_case
        self._classification_service = classification_service
        self._enable_llm_analysis = enable_llm_analysis
        self.metrics = metrics
        
        # Store similarity thresholds from classification service config
        self._primary_similarity_threshold = 0.75  # Default value
        self._fallback_similarity_threshold = 0.6  # Default value
        
        # Try to get config from the classification service
        if hasattr(classification_service, 'config'):
            config = classification_service.config
            if hasattr(config, 'primary_similarity_threshold'):
                self._primary_similarity_threshold = config.primary_similarity_threshold
            if hasattr(config, 'fallback_similarity_threshold'):
                self._fallback_similarity_threshold = config.fallback_similarity_threshold

        logger.info(
            f"ClassifyComplaintsUseCase initialized with "
            f"llm_analysis={enable_llm_analysis}"
        )

    async def execute(self, complaint: ComplaintInput) -> ClassificationResult:
        """
        Execute complete classification workflow for a single complaint.

        Args:
            complaint: Validated complaint input

        Returns:
            ClassificationResult with complete classification information
        """
        workflow_start = datetime.now()

        try:
            logger.info(
                f"Starting classification workflow for case: {complaint.case_id}"
            )

            # Step 1: LLM Analysis (Intent Detection and Summary Generation when enabled)
            intent_start = datetime.now()
            detected_intents = []
            summary = None

            if self._enable_llm_analysis:
                intent_request = IntentDetectionRequest(
                    complaint_id=complaint.case_id,
                    complaint_subject=complaint.subject,
                    complaint_content=complaint.content,
                )

                intent_result = await self._detect_intents.execute(intent_request)
                detected_intents = intent_result.detected_intents
                summary = intent_result.content_summary

                logger.info(
                    f"LLM analysis completed: {len(detected_intents)} intents found, summary generated"
                )

            intent_time = int((datetime.now() - intent_start).total_seconds() * 1000)
            logger.info(f"LLM analysis processed in {intent_time}ms")

            # Step 2: Prepare query text for embedding
            logger.info("Preparing query text for embedding...")
            query_text = self._prepare_query_text(
                complaint.content, detected_intents, summary
            )

            # Step 3: RAG Similarity Search
            logger.info("Executing RAG similarity search...")
            rag_start = datetime.now()

            rag_result = await self._similarity_search.search_similar_categories(
                query_text=query_text,
                similarity_threshold=self._primary_similarity_threshold,
                fallback_threshold=self._fallback_similarity_threshold,
                max_results=5,
            )

            rag_time = int((datetime.now() - rag_start).total_seconds() * 1000)
            logger.info(f"RAG search processed in {rag_time}ms")
            logger.info(
                f"RAG search completed: {len(rag_result.candidates)} candidates found, "
                f"best_score={max([c.similarity_score for c in rag_result.candidates], default=0):.3f}"
            )

            # Step 4: LLM Classification
            llm_start = datetime.now()

            classification_result = await self._classification_service.classify_with_context(
                complaint_input=complaint.content,
                rag_candidates=rag_result.candidates,
                detected_intents=detected_intents,
                summary=summary,
                case_id=complaint.case_id,
            )

            llm_time = int((datetime.now() - llm_start).total_seconds() * 1000)

            # Update processing time with total workflow time
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            classification_result.processing_time_ms = total_time

            # Record metrics
            self.metrics.record_successful_classification(
                classification_result, intent_time, rag_time, llm_time
            )

            logger.info(
                f"Classification workflow completed for {complaint.case_id}: "
                f"{classification_result.sub_category} (confidence: {classification_result.confidence}, "
                f"total_time: {total_time}ms)"
            )

            return classification_result

        except IntentDetectionError as e:
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            logger.error(f"Intent detection failed for {complaint.case_id}: {str(e)}")
            self.metrics.record_failed_classification()
            return self._handle_intent_detection_error(complaint, e, total_time)
        
        except RAGSearchError as e:
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            logger.error(f"RAG search failed for {complaint.case_id}: {str(e)}")
            self.metrics.record_failed_classification()
            return self._handle_rag_search_error(complaint, e, total_time)
            
        except LLMClassificationError as e:
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            logger.error(f"LLM classification failed for {complaint.case_id}: {str(e)}")
            self.metrics.record_failed_classification()
            return self._handle_classification_error(complaint, e, total_time)
            
        except TimeoutError as e:
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            logger.error(f"Classification timeout for {complaint.case_id}: {str(e)}")
            self.metrics.record_failed_classification()
            return self._handle_timeout_error(complaint, e, total_time)
            
        except Exception as e:
            total_time = int((datetime.now() - workflow_start).total_seconds() * 1000)
            logger.error(f"Unexpected error in classification workflow for {complaint.case_id}: {str(e)}")
            self.metrics.record_failed_classification()
            return self._create_error_classification(complaint, str(e), total_time)

    async def execute_batch(
        self, complaints: List[ComplaintInput], concurrency_limit: int = 10
    ) -> List[ClassificationResult]:
        """
        Execute batch classification with controlled concurrency.

        Args:
            complaints: List of complaint inputs to classify
            concurrency_limit: Maximum number of concurrent classifications

        Returns:
            List of classification results in the same order as inputs
        """
        if not complaints:
            return []

        logger.info(
            f"Starting batch classification: {len(complaints)} complaints, "
            f"concurrency_limit={concurrency_limit}"
        )

        semaphore = asyncio.Semaphore(concurrency_limit)

        async def process_with_semaphore(
            complaint: ComplaintInput,
        ) -> ClassificationResult:
            async with semaphore:
                return await self.execute(complaint)

        # Create tasks for all complaints
        tasks = [process_with_semaphore(complaint) for complaint in complaints]

        # Execute with progress tracking
        results = []
        completed = 0

        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(
                    (result.case_id, result)
                )  # Keep track of case_id for ordering
                completed += 1

                if completed % 10 == 0 or completed == len(complaints):
                    logger.info(
                        f"Batch progress: {completed}/{len(complaints)} completed"
                    )

            except Exception as e:
                logger.error(f"Failed to process complaint in batch: {e}")
                completed += 1

        # Sort results to match input order
        result_dict = {case_id: result for case_id, result in results}
        ordered_results = [
            result_dict.get(
                complaint.case_id,
                self._create_error_classification(
                    complaint, "Unknown batch processing error", 0
                ),
            )
            for complaint in complaints
        ]

        logger.info(f"Batch classification completed: {len(ordered_results)} results")
        return ordered_results

    def _prepare_query_text(
        self, original_content: str, intents: List, summary: str = None
    ) -> str:
        """
        Prepare query text for embedding based on LLM analysis results.

        Returns intent + summary when LLM analysis is enabled and data is available,
        otherwise returns original content.
        """
        if self._enable_llm_analysis and intents and summary:
            # Combine intents and summary for enhanced embedding
            intent_text = ", ".join([str(intent) for intent in intents])
            return f"意圖: {intent_text}\\n\\n摘要: {summary}"

        elif self._enable_llm_analysis and intents:
            # Use intents with original content
            intent_text = ", ".join([str(intent) for intent in intents])
            return f"意圖: {intent_text}\\n\\n內容: {original_content}"

        elif self._enable_llm_analysis and summary:
            # Use summary only
            return summary

        else:
            # Use original content
            return original_content

    def _handle_intent_detection_error(
        self, complaint: ComplaintInput, error: IntentDetectionError, processing_time: int
    ) -> ClassificationResult:
        """Handle intent detection errors with graceful degradation."""
        return ClassificationResult(
            case_id=complaint.case_id,
            main_category="其它",
            sub_category="其它",
            sub_description="意圖檢測失敗預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"意圖檢測服務失敗，無法分析內容意圖。錯誤：{str(error)}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )

    def _handle_rag_search_error(
        self, complaint: ComplaintInput, error: RAGSearchError, processing_time: int
    ) -> ClassificationResult:
        """Handle RAG search errors with fallback classification."""
        return ClassificationResult(
            case_id=complaint.case_id,
            main_category="其它",
            sub_category="其它",
            sub_description="相似性搜尋失敗預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"RAG相似性搜尋失敗，無法找到相關類別。錯誤：{str(error)}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )

    def _handle_classification_error(
        self, complaint: ComplaintInput, error: LLMClassificationError, processing_time: int
    ) -> ClassificationResult:
        """Handle LLM classification errors."""
        return ClassificationResult(
            case_id=complaint.case_id,
            main_category="其它",
            sub_category="其它",
            sub_description="LLM分類失敗預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"LLM分類服務失敗。錯誤：{str(error)}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )

    def _handle_timeout_error(
        self, complaint: ComplaintInput, error: TimeoutError, processing_time: int
    ) -> ClassificationResult:
        """Handle timeout errors."""
        return ClassificationResult(
            case_id=complaint.case_id,
            main_category="其它",
            sub_category="其它",
            sub_description="處理超時預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"分類處理超時。錯誤：{str(error)}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )

    def _create_error_classification(
        self, complaint: ComplaintInput, error_message: str, processing_time: int
    ) -> ClassificationResult:
        """Create error classification result."""
        return ClassificationResult(
            case_id=complaint.case_id,
            main_category="其它",
            sub_category="其它",
            sub_description="系統錯誤預設分類",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"工作流程執行失敗，預設分類至其它類別。錯誤原因：{error_message}",
            rag_candidates=[],
            similarity_score=0.0,
            processing_time_ms=processing_time,
        )

    def get_performance_metrics(self) -> dict:
        """Get performance metrics for monitoring and optimization."""
        return self.metrics.get_performance_summary()
