from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, field_serializer


class SubCategory(BaseModel):
    main_category: str = Field(..., description="主案類名稱")
    sub_category: str = Field(..., description="子案類名稱")
    sub_description: str = Field(..., description="子案類描述")
    main_description: str = Field(..., description="主案類描述")
    combined_text: str = Field(..., description="組合文本用於embedding")
    keywords: List[str] = Field(default_factory=list, description="關鍵詞列表")
    embedding: Optional[List[float]] = Field(None, description="向量嵌入")
    created_at: datetime = Field(default_factory=datetime.now, description="創建時間")

    @field_serializer('created_at')
    def serialize_created_at(self, value: datetime) -> str:
        """將created_at格式化為Elasticsearch所需的yyyy/MM/dd HH:mm:ss格式"""
        return value.strftime('%Y/%m/%d %H:%M:%S')
