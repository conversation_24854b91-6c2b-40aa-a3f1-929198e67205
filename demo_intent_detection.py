#!/usr/bin/env python3
"""
Demo script for Intent Detection Engine

This script demonstrates how to use the intent detection engine to analyze
Taiwan government complaint text and detect multiple intents while generating
content summaries.
"""

import asyncio
from pathlib import Path

from dotenv import load_dotenv

from src.application.use_cases.detect_intents import DetectIntentsUseCase
from src.config import Settings
from src.domain.entities.intent_detection_request import IntentDetectionRequest


def load_environment():
    """Load environment variables from .env file."""
    env_path = Path(__file__).parent / ".env"
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ Loaded environment from {env_path}")
    else:
        print(f"⚠️  No .env file found at {env_path}")
        print("Please create a .env file with required API keys")
        return False
    return True


async def demo_intent_detection():
    """Demonstrate intent detection functionality."""

    print("🤖 Intent Detection Engine Demo")
    print("=" * 50)

    # Load environment
    if not load_environment():
        return

    try:
        # Load settings
        settings = Settings()
        config_manager = get_config_manager()
        print("✅ Settings loaded successfully")

        # Create use case with new config system
        intent_config = config_manager.get_intent_detection_config()
        detect_intents_use_case = DetectIntentsUseCase(intent_config)
        print("✅ Intent detection use case initialized")

        # Test cases
        test_cases = [
            # {
            #     "id": "demo-001",
            #     "subject": "關於老莊路往環東路段交通壅塞的陳情",
            #     "content": "反映楊梅市老莊路往環東路的交通問題，尤其是在上下班尖峰時段。此路段常出現嚴重壅塞，從校前路至楊梅市區的路段(環東路口)，由於流量過低，無法有效分散五楊高速的進入車流，導致短短300米的距離需耗時超過10分鐘，且交通事故頻繁發生。\r\n\r\n為改善該路段交通狀況，我懇請市府短期內調配人力，於環東路口進行車流調控。同時，也建議市府長期規劃相關道路調整方案，從根本上解決此處的交通壅塞問題，進而提升市民出行的便利性與安全性。\r\n\r\n謹此陳情，懇請市府重視並妥善處理，造福市民。",
            # },
            {
                "id": "demo-002",
                "subject": "停車場收費不開發票",
                "content": "你好，請問停車場能夠收費但不開發票嗎？\r\n前些天至桃園市中壢區中山路的 中山停車場 停車，到自動繳費機付費時，竟然沒有開立統一發票！\r\n請相關單位查核此停車場 是否有合法登記？\r\n是否有繳交相關營業稅金？\r\n沒開發票的停車場等於是逃漏稅吧！？\r\n中山停車場：\r\nhttps://www.google.com/maps/place/Zhongshan+Parking/@24.9553092,121.2274117,3a,75y,153.87h,90t/data=!3m7!1e1!3m5!1s1Cl-x8LpW6CvBm2sRrLi3w!2e0!6shttps:%2F%2Fstreetviewpixels-pa.googleapis.com%2Fv1%2Fthumbnail%3Fpanoid%3D1Cl-x8LpW6CvBm2sRrLi3w%26cb_client%3Dsearch.gws-prod.gps%26w%3D211%26h%3D120%26yaw%3D153.86594%26pitch%3D0%26thumbfov%3D100!7i16384!8i8192!4m14!1m7!3m6!1s0x34682352f26201a9:0xd0f874756b0e7970!2sZhongshan+Parking!8m2!3d24.9552535!4d121.2274501!16s%2Fg%2F11svzmw5jj!3m5!1s0x34682352f26201a9:0xd0f874756b0e7970!8m2!3d24.9552535!4d121.2274501!16s%2Fg%2F11svzmw5jj?coh=205409&entry=ttu&g_ep=EgoyMDI0MDkxOC4xIKXMDSoASAFQAw%3D%3D",
            },
            # {
            #     "id": "demo-003",
            #     "subject": "期望新增251、5641到職訓局班次",
            #     "content": "非常感謝政府單位照顧到勞工所需，提供上課實習機會，以讓廣大百姓增長所長，但上課路途發現一些不便之處，多數同學也有發現此問題～\r\n而本人因居住地區較其他縣市同學至職訓中心上課較近，故而選擇通勤。上課時間需在8:20前到達職訓上課，故7:20到8:00這段期間無車可搭。做為學生一定會希望自己最晚能在8:15前到，畢竟要爬個小山打卡，需要緩衝時間。\r\n而距離時間點最近的兩班車次251與新竹5641，都會超過八點到站，就算搭車到校一定是遲到，想請問是否與此期間增加到站班次，再麻煩了！🙏🙏🙏"
            # },
            # {
            #     "id": "demo-004",
            #     "subject": "市民公車服務問題客訴",
            #     "content": "今天中午我媽媽於南門市場約12:30左右站欲搭乘225A公車回家，在上車時才將雨傘拐杖撐到車門階梯欲上車時突然間車門即關閉並開走，還好我八十一歲的母親機警放掉拐杖，否則就被公車拖走，你們這是謀殺吧！我媽媽事後就坐計程車追公車找到拐杖，我有拐杖受損照片佐證可以佐證司機的疏忽與惡劣行徑，呈請市長以慈悲胸懷多多體諒這些年長父母們行動不便，要求這些司機不應該以績效為主而是要以民眾安全為主，白天上班時間要搭乘的都是老弱婦孺居多，如果司機素質如此，則是民安全真的堪慮，希望我反應後市長大人能正視這問題不要等到發生意外了才要道歉，市民不要道歉，而是我們反映了要正視問題徹底解決減少意外的悲劇，最後謝謝市長百忙撥冗看信，還請市長多多幫忙這些老人家！誠心希望市長提供給大家的是一輛平安到家的公車而不是奪命公車謝謝！（附上今天被車門夾著跑的雨傘拐杖慘況照片，這如果夾的是我媽媽的腳，應該是一條人命了吧！）"
            # }
        ]

        print(f"\n🔍 Processing {len(test_cases)} test cases...\n")

        for i, case in enumerate(test_cases):
            print(f"📋 Test Case {i}: {case['subject']}")
            print(f"Content: {case['content'][:50]}...")

            # Create request
            request = IntentDetectionRequest(
                complaint_id=case["id"],
                complaint_subject=case["subject"],
                complaint_content=case["content"],
            )

            try:
                # Execute intent detection
                result = await detect_intents_use_case.execute(request)

                if result.error_message:
                    print(f"❌ Error: {result.error_message}")
                else:
                    print(f"✅ Processing completed in {result.processing_time_ms}ms")

                    # Display detected intents
                    print(f"🎯 Detected Intents ({len(result.detected_intents)}):")
                    for intent in result.detected_intents:
                        intent_type = "預設" if intent.is_predefined else "自定義"
                        print(f"   • {intent.intent_name} ({intent_type})")
                        print(f"     理由: {intent.reasoning}")
                        print(f"     證據: {', '.join(intent.text_evidence[:2])}")

                    # Display content summary
                    print(f"📝 Content Summary ({len(result.content_summary)} chars):")
                    print(f"   {result.content_summary}")

            except Exception as e:
                print(f"❌ Execution failed: {str(e)}")

            print("-" * 80)

        # Display metrics
        metrics = detect_intents_use_case.get_metrics_summary()
        if metrics.get("status") != "no_data":
            print("\n📊 Performance Metrics:")
            print(f"   • Total Analyses: {metrics.get('total_analyses', 0)}")
            print(
                f"   • Average Processing Time: {metrics.get('avg_processing_time_ms', 0):.0f}ms"
            )
            print(
                f"   • Dual-task Success Rate: {metrics.get('dual_task_success_rate', 0):.1%}"
            )
            print(
                f"   • Most Common Intents: {list(metrics.get('most_common_intents', {}).keys())[:3]}"
            )

        print("\n🎉 Demo completed successfully!")

    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


async def main():
    """Main entry point."""
    try:
        await demo_intent_detection()
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
