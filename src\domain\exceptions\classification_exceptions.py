"""Domain exceptions for classification operations."""


class ClassificationError(Exception):
    """Base exception for classification operations."""
    
    def __init__(self, message: str, case_id: str = None):
        super().__init__(message)
        self.case_id = case_id


class IntentDetectionError(ClassificationError):
    """Exception raised when intent detection fails."""
    pass


class RAGSearchError(ClassificationError):
    """Exception raised when RAG similarity search fails."""
    pass


class LLMClassificationError(ClassificationError):
    """Exception raised when LLM classification fails."""
    
    def __init__(self, message: str, case_id: str = None, provider: str = None):
        super().__init__(message, case_id)
        self.provider = provider


class ConfigurationError(ClassificationError):
    """Exception raised when configuration is invalid."""
    pass


class TimeoutError(ClassificationError):
    """Exception raised when operations timeout."""
    
    def __init__(self, message: str, case_id: str = None, timeout_seconds: int = None):
        super().__init__(message, case_id)
        self.timeout_seconds = timeout_seconds