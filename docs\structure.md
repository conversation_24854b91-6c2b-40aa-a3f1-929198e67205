---
title: Project Structure
description: "Documents the codebase organization, Clean Architecture implementation, and development conventions."
inclusion: always
---

# Project Structure

## Clean Architecture Implementation

This project follows Clean Architecture principles with clear separation between domain logic, application use cases, and infrastructure concerns.

### Layer Overview

```
src/
├── domain/           # Domain Layer (innermost, no dependencies)
├── application/      # Application Layer (depends on domain)
├── infrastructure/   # Infrastructure Layer (outermost, implements interfaces)
└── config/          # Configuration Layer (cross-cutting concern)
```

## Directory Structure

### Core Application Structure

```
ai-rag-application/
├── src/                                    # Main source code
│   ├── __init__.py
│   ├── main.py                            # Application entry point (enhanced for CLI)
│   ├── app.log                            # Runtime logs (generated)
│   │
│   ├── domain/                            # Domain Layer
│   │   ├── __init__.py
│   │   ├── entities/                      # Business entities
│   │   │   ├── __init__.py
│   │   │   ├── main_category.py          # MainCategory Pydantic model
│   │   │   ├── sub_category.py           # SubCategory Pydantic model
│   │   │   └── complaint_classification.py  # (planned) Classification entities
│   │   └── interfaces/                    # Abstract interfaces
│   │       ├── __init__.py
│   │       ├── embedder_interface.py     # EmbedderInterface ABC
│   │       ├── repository_interface.py   # RepositoryInterface ABC
│   │       ├── agent_interface.py        # (planned) ComplaintClassifierInterface
│   │       └── rag_interface.py          # (planned) RAGRetrieverInterface
│   │
│   ├── application/                       # Application Layer
│   │   ├── __init__.py
│   │   └── use_cases/                     # Business use cases
│   │       ├── __init__.py
│   │       ├── process_categories.py     # ProcessCategoriesUseCase
│   │       └── classify_complaints.py    # (planned) ClassifyComplaintsUseCase
│   │
│   ├── infrastructure/                    # Infrastructure Layer
│   │   ├── __init__.py
│   │   ├── exceptions.py                 # Custom exception classes
│   │   ├── embedders/                    # Embedding implementations
│   │   │   ├── __init__.py
│   │   │   └── gemini_embedder.py       # GeminiEmbedder implementation
│   │   ├── repositories/                 # Data storage implementations
│   │   │   ├── __init__.py
│   │   │   └── elasticsearch_repository.py  # ElasticsearchRepository
│   │   ├── retrievers/                   # (planned) RAG retrieval implementations
│   │   │   └── rag_retriever.py         # ElasticsearchRAGRetriever
│   │   ├── agents/                       # (planned) AI agent implementations
│   │   │   └── complaint_classifier_agent.py  # ComplaintClassifierAgent
│   │   ├── llm/                          # (planned) LLM interface implementations
│   │   │   └── agno_gemini_interface.py # AgnoGeminiInterface
│   │   ├── batch_processing/             # (planned) Memory management
│   │   │   └── memory_manager.py        # Memory-efficient batch processing
│   │   ├── monitoring/                   # (planned) Performance monitoring
│   │   │   └── metrics_collector.py     # Classification metrics
│   │   └── error_handling/               # (planned) Error handling
│   │       └── classification_errors.py  # Error categorization and recovery
│   │
│   ├── config/                           # Configuration Management
│   │   ├── __init__.py
│   │   └── settings.py                   # Pydantic Settings model (enhanced)
│   │
│   ├── data/                             # Static data files
│   │   └── categories_def.json          # Taiwan government complaint categories
│   │
│   └── certs/                           # SSL certificates (environment-specific)
│       └── es/                          # Elasticsearch certificates
│           ├── local/ca/ca.crt
│           ├── dev/ca/ca.crt
│           └── prod/ca/ca.crt
│
├── config/                              # (planned) External configuration files  
│   ├── agents/                          # Agno agent configuration
│   │   └── complaint_classifier.yaml   # Agent settings and prompt templates
│   └── schemas/                         # JSON schemas for validation
│
├── templates/                           # (planned) Prompt templates
│   ├── classification.yaml             # Main classification prompts
│   ├── intent_detection.yaml           # Intent detection prompts  
│   ├── confidence_assessment.yaml      # Confidence evaluation prompts
│   └── simple_classification.yaml      # Fallback classification prompts
│
├── tests/                               # Test files (placeholder)
├── docs/                                # Project documentation
│   ├── product.md                       # Product vision and features
│   ├── tech.md                         # Technology stack details
│   ├── structure.md                    # This file
│   ├── database.md                     # Database schema documentation
│   └── bug_tracking.md                 # Bug tracking log
│
├── specs/                               # Feature specifications
│   └── complaint-classification-agent/ # Current feature specification
│       ├── prd.md                      # Product requirements
│       ├── requirements.md             # Technical requirements
│       ├── design.md                   # Technical design
│       └── tasks.md                    # Implementation tasks
│
├── pyproject.toml                       # Project configuration (uv)
├── uv.lock                             # Locked dependencies
├── .python-version                     # Python version specification
├── .env                                # Environment variables (not in repo)
├── .gitignore                          # Git ignore rules
├── README.md                           # Project overview
└── CLAUDE.md                           # Claude Code instructions
```

## Clean Architecture Layer Details

### Domain Layer (`src/domain/`)

**Purpose**: Contains pure business logic with no external dependencies.

#### Entities (`src/domain/entities/`)
- **MainCategory**: Primary complaint category model with embedding support
- **SubCategory**: Detailed complaint subcategory model with keywords
- **ComplaintClassification** *(planned)*: Classification-specific entities including ComplaintInput, ClassificationResult, RAGCandidate, IntentType enum, and ConfidenceLevel enum

#### Interfaces (`src/domain/interfaces/`)
- **EmbedderInterface**: Abstract base for embedding generation services
- **RepositoryInterface**: Abstract base for data persistence operations
- **AgentInterface** *(planned)*: Abstract interface for complaint classification agents
- **RAGInterface** *(planned)*: Abstract interface for RAG candidate retrieval

**Key Principles**:
- No imports from other layers
- Pure business logic and data models
- Technology-agnostic interfaces

### Application Layer (`src/application/`)

**Purpose**: Orchestrates business workflows and use cases.

#### Use Cases (`src/application/use_cases/`)
- **ProcessCategoriesUseCase**: Main workflow for data processing pipeline
  - Loads JSON data → Parse categories → Generate embeddings → Store in Elasticsearch
- **ClassifyComplaintsUseCase** *(planned)*: Orchestrated complaint classification workflow
  - Combines RAG retrieval with Agno agent classification
  - Supports concurrent batch processing with memory management
  - Comprehensive error handling and fallback strategies

**Key Principles**:
- Depends only on domain layer
- Coordinates between different domain services
- Contains no implementation details

### Infrastructure Layer (`src/infrastructure/`)

**Purpose**: Implements domain interfaces with concrete technologies.

#### Embedders (`src/infrastructure/embedders/`)
- **GeminiEmbedder**: Google Gemini API integration for embedding generation
  - Implements `EmbedderInterface`
  - Handles async batch processing
  - Rate limiting and error handling

#### Repositories (`src/infrastructure/repositories/`)
- **ElasticsearchRepository**: Vector database operations
  - Implements `RepositoryInterface`
  - Index management and bulk operations
  - SSL/TLS configuration per environment

#### Retrievers *(planned)*
- **ElasticsearchRAGRetriever**: RAG candidate retrieval using existing infrastructure
  - Implements `RAGRetrieverInterface`
  - Integrates with GeminiEmbedder for query embeddings
  - Supports similarity search with configurable thresholds

#### Agents *(planned)*
- **ComplaintClassifierAgent**: Agno framework integration for AI-based classification
  - Implements `ComplaintClassifierInterface`
  - Multi-intent detection with confidence assessment
  - Structured output with error recovery

#### LLM Interfaces *(planned)*
- **AgnoGeminiInterface**: Agno-compatible Gemini Chat API integration
  - Supports structured JSON output
  - Implements retry mechanisms and error handling

#### Error Handling *(planned)*
- **ClassificationErrors**: Comprehensive error categorization and recovery strategies
  - Specific handlers for different error types
  - Intelligent fallback mechanisms

#### Exceptions (`src/infrastructure/exceptions.py`)
- **ElasticsearchBulkError**: Batch operation failures with detailed diagnostics
- **ElasticsearchConnectionError**: Connection and authentication issues

**Key Principles**:
- Implements domain interfaces
- Contains all external system integrations
- Handles technology-specific concerns

### Configuration Layer (`src/config/`)

**Purpose**: Environment-aware configuration management.

- **Settings**: Pydantic-based settings with environment variable support
- Multi-environment support (local/dev/prod)
- Secure credential management

## File Naming Conventions

### Python Files
- **snake_case** for all Python files and directories
- **Descriptive names** that indicate purpose
- **Interface suffix** for abstract base classes (`_interface.py`)

### Data Files
- **descriptive_names.json** for JSON data files
- **Environment-specific paths** for certificates and config

### Documentation
- **lowercase.md** for documentation files
- **Descriptive names** that indicate content area

## Import Conventions

### Layer Dependencies
```python
# Domain layer - NO external dependencies
from pydantic import BaseModel
from datetime import datetime

# Application layer - Domain only
from src.domain.entities import MainCategory
from src.domain.interfaces import EmbedderInterface

# Infrastructure layer - Domain + external libraries
from src.domain.interfaces import RepositoryInterface
from elasticsearch import AsyncElasticsearch
```

### Project Structure Rules
1. **Absolute imports** from project root: `from src.domain.entities import MainCategory`
2. **Relative imports** within same package: `from .main_category import MainCategory`
3. **Interface dependencies** through dependency injection

## Development Workflow

### Adding New Features

1. **Domain First**: Define entities and interfaces in domain layer
2. **Application Logic**: Create use cases in application layer
3. **Infrastructure**: Implement concrete classes in infrastructure layer
4. **Configuration**: Update settings if needed

### Adding New External Services

1. Define interface in `src/domain/interfaces/`
2. Create implementation in appropriate `src/infrastructure/` subdirectory
3. Register in dependency injection (main.py)
4. Add configuration to `src/config/settings.py`

### Data Processing Pipeline Structure

```
JSON Data → Parse Categories → Generate Embeddings → Store in Elasticsearch
     ↓            ↓                    ↓                      ↓
data/*.json → UseCase Logic → GeminiEmbedder → ElasticsearchRepository
```

## Environment-Specific Patterns

### Configuration Management
- Environment detection via `ENVIRONMENT` variable
- Certificate paths: `src/certs/es/{environment}/ca/`
- Settings inheritance with environment overrides

### Deployment Structure
- **local**: Development with local Elasticsearch
- **dev**: Development environment with shared resources
- **prod**: Production environment with full security

## Code Organization Principles

### Single Responsibility
- Each class has one reason to change
- Clear separation between data models, business logic, and infrastructure

### Dependency Inversion
- High-level modules don't depend on low-level modules
- Both depend on abstractions (interfaces)

### Interface Segregation
- Clients don't depend on interfaces they don't use
- Focused, cohesive interface definitions

### Open/Closed Principle
- Open for extension (new implementations)
- Closed for modification (stable interfaces)

## Architecture Benefits

This Clean Architecture implementation provides:

### Maintainability
- **Clear Separation**: Domain logic isolated from infrastructure concerns
- **Dependency Inversion**: High-level modules independent of implementation details
- **Single Responsibility**: Each component has one clear purpose

### Testability
- **Interface-based Design**: Easy mocking of external dependencies
- **Pure Domain Logic**: Business rules without external dependencies
- **Isolated Components**: Each layer can be tested independently

### Scalability
- **Async Processing**: Non-blocking I/O for high throughput
- **Batch Operations**: Efficient processing of large datasets
- **Configurable Components**: Environment-specific optimizations

### Traditional Chinese Support
- **UTF-8 Encoding**: Proper handling throughout the pipeline
- **Chinese Text Analysis**: Elasticsearch ik_max_word analyzer
- **Structured Formatting**: Optimized for Chinese complaint text embedding

This architecture specifically supports the Taiwan government complaint processing domain while maintaining flexibility for future enhancements.