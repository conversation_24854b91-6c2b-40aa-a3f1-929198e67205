from abc import ABC, abstractmethod
from typing import List, Dict, Any
from ..entities import MainCategory, SubCategory


class RepositoryInterface(ABC):
    """資料儲存庫介面"""
    
    @abstractmethod
    async def create_indices(self) -> None:
        """創建Elasticsearch索引"""
        pass
    
    @abstractmethod
    async def save_main_category(self, main_category: MainCategory) -> None:
        """
        儲存主案類
        
        Args:
            main_category: 主案類實體
        """
        pass
    
    @abstractmethod
    async def save_sub_category(self, sub_category: SubCategory) -> None:
        """
        儲存子案類
        
        Args:
            sub_category: 子案類實體
        """
        pass
    
    @abstractmethod
    async def save_main_categories_batch(self, main_categories: List[MainCategory]) -> None:
        """
        批次儲存主案類
        
        Args:
            main_categories: 主案類實體列表
        """
        pass
    
    @abstractmethod
    async def save_sub_categories_batch(self, sub_categories: List[SubCategory]) -> None:
        """
        批次儲存子案類
        
        Args:
            sub_categories: 子案類實體列表
        """
        pass
    
    @abstractmethod
    async def delete_indices(self) -> None:
        """刪除索引（用於重新建立）"""
        pass