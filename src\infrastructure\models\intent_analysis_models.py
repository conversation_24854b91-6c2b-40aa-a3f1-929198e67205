"""Pydantic response models for structured LLM intent analysis output."""

from pydantic import BaseModel, Field
from typing import List


class DetectedIntentModel(BaseModel):
    """Pydantic model for individual detected intent with validation."""
    
    intent_name: str = Field(..., description="意圖名稱")
    is_predefined: bool = Field(..., description="是否為預設類別")
    reasoning: str = Field(..., description="判斷理由")
    text_evidence: List[str] = Field(..., description="支持文字證據陣列")


class IntentAnalysisResponse(BaseModel):
    """Main response model for intent analysis ensuring 100% format compliance."""
    
    detected_intents: List[DetectedIntentModel] = Field(..., description="識別出的意圖陣列")
    content_summary: str = Field(..., description="內容重點摘要（100-300字）")