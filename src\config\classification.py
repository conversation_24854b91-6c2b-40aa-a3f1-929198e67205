"""Classification configuration settings."""

from typing import TYPE_CHECKING

from pydantic import Field, field_validator

from .base_llm_config import BaseConfig, SimilarityConfig
from .llm import APIProvider, LLMSettings

if TYPE_CHECKING:
    from .llm import LLMProviderConfig


class ClassificationSettings(BaseConfig):
    """Configuration for LLM-based classification service."""

    # LLM Configuration
    llm_settings: LLMSettings = Field(default_factory=LLMSettings)

    # Provider Configuration
    llm_provider: APIProvider = Field(default=APIProvider.GOOGLE)

    # Model Configuration
    max_tokens: int = Field(default=500, ge=100, le=2000)
    temperature: float = Field(default=0.1, ge=0.0, le=1.0)

    # Feature Flags
    enable_llm_analysis: bool = Field(default=False)

    # Similarity Configuration
    similarity: SimilarityConfig = Field(default_factory=SimilarityConfig)

    # Performance Settings
    request_timeout: float = Field(default=30.0, ge=5.0, le=120.0)

    @field_validator("llm_provider")
    @classmethod
    def validate_llm_provider(cls, v: APIProvider) -> APIProvider:
        """Validate LLM provider."""
        if not APIProvider.is_valid_provider(v.value):
            valid_providers = APIProvider.get_all_providers()
            raise ValueError(f"llm_provider must be one of: {valid_providers}")
        return v

    def get_current_model_id(self) -> str:
        """Get the current model ID based on selected provider."""
        return self.llm_settings.get_default_model(self.llm_provider)

    def get_api_key_for_provider(self) -> str | None:
        """Get API key for current provider."""
        return self.llm_settings.get_api_key(self.llm_provider)

    def get_provider_config(self) -> "LLMProviderConfig | None":
        """Get provider configuration."""
        return self.llm_settings.get_provider_config(self.llm_provider)

    def is_provider_available(self) -> bool:
        """Check if the current provider is available."""
        return self.llm_settings.is_provider_available(self.llm_provider)

    def get_primary_similarity_threshold(self) -> float:
        """Get primary similarity threshold."""
        return self.similarity.primary_similarity_threshold

    def get_fallback_similarity_threshold(self) -> float:
        """Get fallback similarity threshold."""
        return self.similarity.fallback_similarity_threshold

    def get_generation_config(self) -> dict:
        """Get generation configuration for classification."""
        return {
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.request_timeout,
            "model": self.get_current_model_id(),
        }
