# 桃園市政府陳情案件 AI 自動分類 POC 簡報

## 一、POC 目標

### 核心目標

-   **建立 AI 驅動的陳情案件自動分類系統**
-   **達成>80%分類準確度**
-   **提供 Playground UI 界面**，便於客戶實際操作驗證
-   **支援未來系統整合需求**

## 二、系統架構與技術方案

### 核心技術堆疊

-   **LLM 模型**: Google gemini-2.5-flash / ChatGPT gpt-5-mini / Gemma3 27B
-   **Embedding 模型**: Google gemini-embedding-001
-   **向量資料庫**: Elasticsearch 8.13.2 (3072 維度向量搜索)
-   **開發語言**: Python

### 系統運作流程（三種方法通用）

1. **資料準備**: 整理 175 個案類定義（主類別+子類別）

2. **案件輸入**: 接收 JSON 格式陳情案件批次檔案，包含欄位: case_id, subject, content, ground_truth_category

3. **智能分類處理**:

    - 方案一：單一 prompt: 整合所有案類定義至單一 prompt，一次性送入 LLM 完成分類
    - 方案二：Prompt Chain : 先判斷主類別，再根據主類別判斷子類別，進而縮小候選範圍
    - 方案三：生成案類向量 embeddings 並建立索引，接著進行向量搜索（相似度>0.75）以縮小候選範圍主案類與子案類定義，再由 LLM 精確分類

4. **結果輸出**: JSON 格式含類別、信心度、分類理由、ground_truth_category

## 三、分類方法比較與選擇

### 三種技術方案對比

| 方案                     | 技術實現                                                 | 優點                                        | 缺點                                                             | 每筆平均耗時 |
| ------------------------ | -------------------------------------------------------- | ------------------------------------------- | ---------------------------------------------------------------- | ------------ |
| **方案一：單一 Prompt**  | 將所有 175 個案類定義塞入單一 prompt 中，一次完成分類    | • 實現簡單<br>• 開發快速<br>• 單次 API 調用 | • Context 過長影響準確率<br>• Token 成本高<br>• 難以優化特定類別 | < 2 秒       |
| **方案二：Prompt Chain** | 使用 Agent 框架實作 prompt chain，先判斷主類，再判斷子類 | • Context 較短，準確率預期可更高            | • 需多次 API 調用<br>• 開發複雜度較高<br>• 響應時間較長          | 5-10 秒      |
| **方案三：RAG**          | 向量檢索縮小候選範圍，再由 LLM 精確分類                  | • Context 最短                              | • 需要向量資料庫<br>• 開發複雜度最高<br>• 需維護 embeddings      | 3-5 秒       |

## 四、實施階段規劃

### Phase 1: 內部測試與方法評估 (Week 1-3)

-   **資料量**: 使用 2025/07/01 ~ 2025/07/31，每類別 5 筆，共 875 筆測試資料
-   **目標**:

    -   實作三種分類方法（單一 Prompt、Prompt Chain、RAG）
    -   實驗三種模型
    -   選定最佳技術和模型方案
    -   完成內部(RD/PM/業務)驗證
    -   紀錄爭議案例

-   **實驗設計**

    -   記錄各方法的準確率、處理時間、API 成本
    -   紀錄各 LLM 模型的準確率、處理時間、API 成本
    -   分析錯誤案例不斷迭代優化 prompt 、案類定義描述和架構

-   **評估指標**

    -   主要指標：分類準確率（權重 80%）
    -   次要指標：處理速度（10%）、成本（10%）

-   **決策機制**

    -   根據綜合評分選擇最佳方案
    -   以方法一（單一 Prompt）作為 baseline 進行對照
    -   資料集包含人工標記的正確答案 (ground_truth_category) 作為評估基準

### Phase 2: 客戶品質驗證 (Week 4-5)

-   **資料量**: 使用 2025/07/01 ~ 2025/07/31，每類別 10 筆，共 1,750 筆資料
-   **目標**:
    -   客戶逐筆驗證
    -   收集回饋意見
    -   調整優化方案

### Phase 3: 全面驗證階段 (Week 6-8)

-   **資料量**: 使用 2025/07/01 ~ 2025/07/31 全部資料，共約 12,000 筆資料
-   **目標**:
    -   系統穩定性驗證
    -   大規模資料處理能力測試
    -   準確率最終確認
    -   效能基準測試

## 五、工時估算

### 開發工時 (人天)

| 階段     | 工作項目              | 工時        |
| -------- | --------------------- | ----------- |
| Phase 1  | 單一 Prompt 實作      | 2 天        |
|          | Prompt Chain 實作     | 3 天        |
|          | RAG 實作              | 5 天        |
|          | 內部測試驗證&迭代優化 | 7 天        |
| Phase 2  | 客戶測試反饋          | 5 天        |
|          | 調整優化架構          | 5 天        |
| Phase 3  | 客戶測試反饋          | 5 天        |
|          | 調整優化架構          | 5 天        |
|          | Buffer (20%)          | 6 天        |
| **總計** |                       | **36 人天** |

> 註：客戶驗證階段（Phase 2 & 3）的「客戶測試反饋」工時為客戶方作業時間，不計入開發團隊工時。實際開發團隊工時為 36 人天。

## 六、Token 使用量與費用預估

### Token 計算基礎

-   API Input Token: 0.4 USD / 1M token (以 gpt-4.1-mini 為例)

    -   Gemini-2.5-Flash 為 0.3 USD/1M tokens
    -   gpt-5-mini 為 0.25 USD/1M tokens
    -   gpt-4.1-mini 為 0.4 USD/1M tokens

-   單一 Prompt:

    -   平均每筆陳情: ~200 tokens
    -   案類定義 prompt: ~32000 tokens/次
    -   輸出回應: ~100 tokens/筆
    -   單筆成本: 0.012 USD

-   Prompt Chain:

    -   平均每筆陳情: ~200 tokens
    -   案類定義 prompt: ~10000 tokens/次
    -   輸出回應: ~100 tokens/筆
    -   單筆成本: 0.004 USD

-   RAG:
    -   平均每筆陳情: ~200 tokens
    -   案類定義 prompt: ~5000 tokens/次
    -   輸出回應: ~100 tokens/筆
    -   單筆成本: 0.002 USD

### 各階段 Token 使用量

| 階段     | 資料量※  | 費用(USD)                                          | 備註                                                                                |
| -------- | -------- | -------------------------------------------------- | ----------------------------------------------------------------------------------- |
| Phase 1  | 1000 筆  | 1000\*0.012+1000\*0.004\*100+1000\*0.002\*100=$600 | 三種方法測試，其中方法 1 為 baseline 只會執行 1 次, 方法 2,3 經過測試迭代 約 100 次 |
| Phase 2  | 2000 筆  | 2000\*0.004\*30=$240                               | 假設 Phase1 最終選擇方法 2, 約測試 30 次                                            |
| Phase 3  | 12000 筆 | 12000\*0.004\*10=$480                              | 假設最後使用全部資料集 約測試 10 次                                                 |
| **總計** | xxx      | **$1320**                                          |                                                                                     |

※ 資料量取整數方便估算（實際 Phase 1: 875 -> 1000 筆，Phase 2: 1,750 -> 2,000 筆）

### 總 API 費用: 抓$1500 USD 上下

> ※ 以上為概略估算值，實際費用會因模型選擇、prompt 優化程度而有所變動（±20%）

### 各方法成本細分

| 方法        | 每筆平均成本(USD) | 月處理 12000 筆預估(USD) |
| ----------- | ----------------- | ------------------------ |
| 單一 Prompt | $0.012            | ~$150                    |
| Agent Chain | $0.004            | $50                      |
| RAG         | $0.002            | $25                      |

## 七、資料集規劃

### 測試資料來源

-   **時間範圍**: 2025/07/01 ~ 2025/07/31
-   **總資料量**: 約 12,000 筆陳情案件
-   **使用策略**:
    -   Phase 1: 隨機抽樣每類別 5 筆
    -   Phase 2: 擴大抽樣每類別 10 筆
    -   Phase 3: 全部資料

**抽樣策略說明**：

-   Phase 1 採少量樣本（5 筆/類）進行快速迭代測試，降低初期調整成本
-   Phase 2 採適量樣本（10 筆/類）供客戶實際驗證，平衡驗證品質與時間成本
-   Phase 3 採全量資料進行最終驗證，確保系統穩定性

### 資料品質要求

-   涵蓋所有 175 個案類 (O)
-   包含多意圖複雜案件 (X) ※需客戶或 PM 協助提供代表性樣本
-   保留邊緣案例用於測試 (X) ※需人力逐筆標記與確認
-   確保資料代表性 (X) ※需客戶協助篩選具代表性的案例
