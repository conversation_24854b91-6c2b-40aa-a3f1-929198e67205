# Technical Design: Classification Logic Orchestrator

## Overview

This document details the technical implementation of the Classification Logic Orchestrator, the main use case that coordinates the complete complaint classification workflow by performing intent detection and summary generation (when enabled) before embedding, then integrating with RAG similarity search to produce final classifications through direct LLM processing.

## Architecture Design

### Clean Architecture Alignment

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              ClassifyComplaintsUseCase                  │ │
│  │                                                         │ │
│  │  ┌─────────────────┐                                    │ │
│  │  │ Intent Detection│                                   │ │
│  │  │ Engine          │                                   │ │
│  │  └─────────────────┘                                    │ │
│  │                   ↓                                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │         RAG Similarity Search                       │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                   ↓                                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │    LLM Classification Service (Abstract)            │ │ │
│  │  │  ┌─────────────────────────────────────────────────┐ │ │ │
│  │  │  │           Agno Agent Integration                │ │ │ │
│  │  │  │  • OpenAI Chat (GPT-4o)                        │ │ │ │
│  │  │  │  • Anthropic Claude (Sonnet-4)                 │ │ │ │
│  │  │  │  • Google Gemini (2.5-flash)                   │ │ │ │
│  │  │  └─────────────────────────────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ComplaintInput │ ClassificationResult │ IntentType         │
│                 │ LLMProvider Interface                      │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                        │
│  GeminiEmbedder │ ElasticsearchRepository │ Configuration   │
│  AgnoLLMProvider │ Multi-LLM Support via Agno Framework     │
└─────────────────────────────────────────────────────────────┘
```

### Component Responsibilities

**ClassifyComplaintsUseCase**: Main orchestrator implementing the complete classification workflow
**IntentDetectionService**: Handles intent analysis and summary generation before embedding generation when enabled (optional)
**LLMClassificationService**: Infrastructure service that handles LLM integration for final classification decisions using abstract provider interface, with direct confidence output and built-in fallback to default category

## Data Flow Architecture

### Primary Classification Workflow

```mermaid
graph TD
    A[ComplaintInput] --> B[Input Validation]
    B --> C["Intent Detection + Summary Generation*"]
    C --> D[Generate Embedding]
    D --> E[RAG Similarity Search]
    E --> F[LLM Classification Context]
    F --> G[LLM Processing]
    G --> H[ClassificationResult]

    I[Error Handling] --> J[Default Category Assignment]
    J --> H

    Note1["*Single LLM call handles both tasks when enabled"]
    Note2["Default category: 其它 with failure reason"]
```

### Data Transformation Pipeline

1. **Input Processing**: `ComplaintInput` → Validated complaint data
2. **Intent + Summary Detection**: Text content → `IntentDetectionResult` with intents and summary with single LLM call(when enabled)
3. **Embedding Generation**: Intent + Summary OR original text → 3072-dimensional vector
4. **RAG Search Results**: Vector → List[SimilarityMatch] with scores and categories
5. **Context Integration**: RAG + Intents + Original content → Structured LLM prompt context
6. **LLM Classification**: Context → Category decision with confidence and reasoning
7. **Result Assembly**: LLM output → Final `ClassificationResult`

## Implementation Specifications

### ClassifyComplaintsUseCase

```python
class ClassifyComplaintsUseCase:
    """Main orchestrator for complaint classification workflow."""

    def __init__(
        self,
        detect_intents_use_case: DetectIntentsUseCase,
        similarity_search_use_case: SimilaritySearchUseCase,
        llm_classification_service: LLMClassificationService,
        settings: ClassificationSettings,
    ):
        self._detect_intents = detect_intents_use_case
        self._similarity_search = similarity_search_use_case
        self._llm_classification = llm_classification_service
        self._settings = settings

    async def execute(
        self,
        complaint: ComplaintInput
    ) -> ClassificationResult:
        """Execute complete classification workflow."""
        try:
            return await self._llm_classification.classify_with_context(...)
        except Exception as e:
            logger.error(f"Classification workflow failed: {e}")
            # LLM service handles fallback internally
            raise

    async def execute_batch(
        self,
        complaints: List[ComplaintInput],
        concurrency_limit: int = 10
    ) -> List[ClassificationResult]:
        """Execute batch classification with concurrency control."""

    # Summary generation is now handled within DetectIntentsUseCase
    # when enable_llm_analysis is True, eliminating the need
    # for separate summary generation logic here
    # Error handling is simplified - LLM service provides built-in fallback
```

### LLM Classification Service

````python
from abc import ABC, abstractmethod
from typing import Protocol

class LLMProvider(Protocol):
    """Abstract interface for LLM providers."""

    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 4096
    ) -> str:
        """Generate text response from LLM."""
        ...

class LLMClassificationService:
    """Infrastructure service that handles LLM integration for final classification decisions."""

    def __init__(
        self,
        llm_provider: LLMProvider,
        settings: Settings
    ):
        self._llm_provider = llm_provider
        self._settings = settings

    async def classify_with_context(
        self,
        original_complaint: str,
        rag_results: List[SimilarityMatch],
        detected_intents: List[IntentType],
        summary: Optional[str] = None,
        similarity_threshold: float = 0.75
    ) -> ClassificationResult:
        """Generate classification using structured context with direct confidence output."""
        try:
            # Normal classification logic here
            pass
        except Exception as e:
            return self._create_default_classification(original_complaint, str(e))

    def _create_default_classification(
        self,
        complaint: str,
        failure_reason: str
    ) -> ClassificationResult:
        """Create default classification when processing fails."""
        return ClassificationResult(
            category="其它",
            main_category="其它",
            intents=[],
            confidence=ConfidenceLevel.LOW,
            reasoning=f"分類失敗，預設分類至其它類別。失敗原因：{failure_reason}"
        )

### Agno Agent Implementation

The `LLMClassificationService` can be implemented using the Agno Agent framework for better LLM abstraction and provider support:

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.models.google import Gemini
from pydantic import BaseModel, Field
from typing import List, Optional

class ClassificationResponse(BaseModel):
    """Structured response model for classification results."""
    category: str = Field(..., description="Selected sub-category")
    main_category: str = Field(..., description="Corresponding main category")
    confidence: str = Field(..., description="High or Low confidence level")
    reasoning: str = Field(..., min_length=10, description="Classification reasoning")

class AgnoLLMClassificationService(LLMClassificationService):
    """Agno-based implementation of LLM Classification Service."""

    def __init__(self, model_provider: str, api_key: str, settings: Settings):
        super().__init__(None, settings)  # No direct LLM provider needed

        # Configure model based on provider
        if model_provider.lower() == "openai":
            self._model = OpenAIChat(id="gpt-4o", api_key=api_key)
        elif model_provider.lower() == "anthropic":
            self._model = Claude(id="claude-sonnet-4-20250514", api_key=api_key)
        elif model_provider.lower() == "google":
            self._model = Gemini(id="gemini-2.5-flash", api_key=api_key)
        else:
            raise ValueError(f"Unsupported model provider: {model_provider}")

        # Initialize Agno Agent with structured output
        self._classification_agent = Agent(
            model=self._model,
            description="台灣政府陳情案件分類專家，專門根據案件內容和相似度結果進行精確分類",
            instructions=self._build_classification_instructions(),
            response_model=ClassificationResponse,
            temperature=0.1,
            markdown=False,
        )

    async def classify_with_context(
        self,
        original_complaint: str,
        rag_results: List[SimilarityMatch],
        detected_intents: List[IntentType],
        summary: Optional[str] = None,
        similarity_threshold: float = 0.75
    ) -> ClassificationResult:
        """Generate classification using Agno Agent with structured output."""
        try:
            # Build classification prompt
            prompt = self._build_classification_prompt(
                original_complaint, rag_results, detected_intents, summary
            )

            # Get structured response from Agno Agent
            response = await self._classification_agent.arun(prompt)

            # Convert to our domain model
            return ClassificationResult(
                category=response.response_model.category,
                main_category=response.response_model.main_category,
                intents=detected_intents,
                confidence=response.response_model.confidence,
                reasoning=response.response_model.reasoning
            )

        except Exception as e:
            logger.error(f"Agno classification failed: {e}")
            return self._create_default_classification(original_complaint, str(e))

    def _build_classification_instructions(self) -> str:
        """Build comprehensive instructions for the classification agent."""
        return """
        你是一個台灣政府陳情案件分類專家。請根據提供的資訊進行分類：

        分類原則：
        1. 仔細分析陳情內容的核心訴求和意圖
        2. 參考相似度搜尋結果選擇最適合的類別
        3. 確保主類別與子類別的一致性
        4. 如果對分類很有把握且相似度高，輸出 "high" 信心度
        5. 如果有不確定性或相似度偏低，輸出 "low" 信心度
        6. 如果沒有合適的類別匹配，分類到 "其它" 並說明原因

        回應格式：
        - category: 選定的子類別名稱
        - main_category: 對應的主類別名稱
        - confidence: "high" 或 "low"
        - reasoning: 詳細的分類理由和信心度判斷依據
        """

    def _build_classification_prompt(
        self,
        complaint: str,
        rag_results: List[SimilarityMatch],
        intents: List[IntentType],
        summary: Optional[str] = None
    ) -> str:
        """Build structured prompt for classification."""

        # Build complaint content section
        content_parts = [f"原始陳情內容：{complaint}"]
        if intents:
            content_parts.append(f"偵測到的意圖：{', '.join([i.value for i in intents])}")
        if summary:
            content_parts.append(f"內容摘要：{summary}")

        complaint_section = "\n".join(content_parts)

        # Build RAG results section
        rag_section = "相似類別搜尋結果：\n"
        for idx, match in enumerate(rag_results[:5], 1):
            rag_section += f"{idx}. 類別：{match.category} (主類別：{match.main_category})\n"
            rag_section += f"   描述：{match.description}\n\n"

        return f"""請根據以下資訊進行陳情案件分類：

## 陳情案件資訊
{complaint_section}

## {rag_section}

請提供分類結果：
"""
````

### Legacy Implementation (Non-Agno)

For reference, the original implementation without Agno:

```python
class LegacyLLMClassificationService(LLMClassificationService):
    """Traditional implementation using direct LLM provider calls."""

    async def classify_with_context(
        self,
        original_complaint: str,
        rag_results: List[SimilarityMatch],
        detected_intents: List[IntentType],
        summary: Optional[str] = None,
        similarity_threshold: float = 0.75
    ) -> ClassificationResult:
        """Generate classification using direct LLM provider calls."""
        try:
            prompt = self._build_prompt(original_complaint, rag_results, detected_intents, summary)
            response = await self._llm_provider.generate_text(
                prompt=prompt,
                temperature=self._settings.llm_temperature,
                max_tokens=self._settings.llm_max_tokens
            )
            return self._parse_response(response, detected_intents)
        except Exception as e:
            return self._create_default_classification(original_complaint, str(e))
```

### Prompt Engineering Design

#### Updated Classification Prompt Strategy (Agno-Compatible)

The prompt template has been updated to work with Agno's structured output system. Instead of requiring manual JSON formatting, Agno automatically handles structured responses using Pydantic models:

**Key Advantages of Agno Approach:**

1. **Automatic Structured Output**: No manual JSON parsing required
2. **Built-in Validation**: Pydantic models ensure response format consistency
3. **Multi-Provider Support**: Easy switching between OpenAI, Anthropic, Google
4. **Retry Logic**: Built-in error handling and retry mechanisms
5. **Better Prompt Engineering**: Agent instructions separate from prompts

**Legacy Prompt Template (for reference only):**

```python
# This template is replaced by the Agno Agent approach above
LEGACY_CLASSIFICATION_PROMPT_TEMPLATE = """
你是一個台灣政府陳情案件分類專家。請根據以下資訊對陳情內容進行分類：

## 陳情內容
{complaint_content}

## 案件類別定義描述
{rag_similarity_results}

## 分類指引
- 結合陳情內容的意圖和主要訴求進行判斷
- 確保主類別與子類別一致性
- 如果對分類結果很有把握，請輸出High信心度
- 如果有任何不確定性或模糊之處，請輸出Low信心度
- 如果陳情內容與提供的任何類別都不匹配，請分類到"其它"類別並在reasoning中說明原因

請提供 JSON 格式回應：
{{
    "category": "選定的子類別",
    "main_category": "對應的主類別",
    "confidence": "High或Low",
    "reasoning": "案件分類理由和信心度判斷依據"
}}
"""

def build_complaint_content(original_text: str, intents: List[str] = None, summary: str = None) -> str:
    """根據feature flags組合陳情內容。"""

    if intents or summary:
        # 當有intent或summary時，提供結構化資訊
        content_parts = [f"原始內容：{original_text}"]

        if intents:
            content_parts.append(f"偵測意圖：{', '.join(intents)}")

        if summary:
            content_parts.append(f"內容摘要：{summary}")

        return "\n\n".join(content_parts)
    else:
        # 當flags為disable時，直接使用原始內容
        return original_text
```

#### Structured Output Validation

```python
class LLMClassificationResponse(BaseModel):
    """Structured LLM response with validation."""

    category: str = Field(..., description="Selected sub-category")
    main_category: str = Field(..., description="Corresponding main category")
    confidence: str = Field(..., description="High or Low confidence level")
    reasoning: str = Field(..., min_length=10, description="Classification reasoning and confidence justification")

    @validator('category')
    def validate_category_exists(cls, v):
        # Validate against known categories
        pass

    @validator('main_category')
    def validate_category_consistency(cls, v, values):
        # Ensure main/sub category consistency
        pass

    @validator('confidence')
    def validate_confidence_level(cls, v):
        # Ensure confidence is either "high" or "low"
        if v not in ["high", "low"]:
            raise ValueError("Confidence must be either 'High' or 'Low'")
        return v
```

### Concurrent Processing Design

#### Async Batch Processing

```python
async def execute_batch(
    self,
    complaints: List[ComplaintInput],
    concurrency_limit: int = 10
) -> List[ClassificationResult]:
    """Execute batch classification with controlled concurrency."""

    semaphore = asyncio.Semaphore(concurrency_limit)

    async def process_with_semaphore(complaint: ComplaintInput) -> ClassificationResult:
        async with semaphore:
            return await self.execute(complaint)

    # Create tasks for all complaints
    tasks = [
        process_with_semaphore(complaint)
        for complaint in complaints
    ]

    # Execute with progress tracking
    results = []
    for coro in asyncio.as_completed(tasks):
        try:
            result = await coro
            results.append(result)
            logger.info(f"Processed complaint {len(results)}/{len(complaints)}")
        except Exception as e:
            logger.error(f"Failed to process complaint: {e}")
            # Handle individual failures gracefully

    return results
```

#### Resource Management

```python
class ResourceManager:
    """Manages API rate limits and connection pooling."""

    def __init__(self, settings: Settings):
        self._llm_rate_limiter = AsyncRateLimiter(
            calls=60,  # LLM API limit
            period=60  # Per minute
        )
        self._elasticsearch_pool = ConnectionPool(
            max_connections=settings.elasticsearch_max_connections
        )

    async def acquire_llm_slot(self):
        """Acquire slot for LLM API call."""
        await self._llm_rate_limiter.acquire()

    async def acquire_elasticsearch_connection(self):
        """Acquire Elasticsearch connection from pool."""
        return await self._elasticsearch_pool.acquire()
```

### Error Handling Design

#### Simplified Fallback Strategy

Classification failures are handled directly within the LLM Classification Service through a simple default category assignment. When classification fails for any reason, or when the LLM determines that none of the provided RAG categories match the complaint content, the system assigns the complaint to the default "其它" category with a clear explanation in the reasoning field.

#### Error Recovery Patterns

```python
@retry(
    stop=stop_after_attempt(2),
    wait=wait_fixed(2),
    retry=retry_if_exception_type((GeminiAPIError, ElasticsearchConnectionError))
)
async def _execute_with_retry(
    self,
    complaint: ComplaintInput
) -> ClassificationResult:
    """Execute classification with automatic retry and simple fallback."""
    try:
        # Attempt normal classification
        return await self._normal_classification_flow(complaint)
    except Exception as e:
        logger.warning(f"Classification failed: {e}")
        return self._create_default_classification(
            complaint.content,
            f"系統處理失敗：{str(e)}"
        )

async def _handle_processing_error(
    self,
    complaint: ComplaintInput,
    error: Exception
) -> ClassificationResult:
    """Handle processing errors with default category fallback."""
    return self._create_default_classification(
        complaint.content,
        f"處理錯誤：{type(error).__name__} - {str(error)}"
    )
```

### Configuration Extensions

#### Classification Settings

```python
class ClassificationSettings(BaseModel):
    """Extended settings for classification orchestrator with Agno support."""

    # Similarity thresholds
    primary_similarity_threshold: float = 0.75
    fallback_similarity_threshold: float = 0.6

    # Feature flags
    enable_llm_analysis: bool = True

    # Performance settings
    max_concurrent_classifications: int = 10
    classification_timeout_seconds: int = 2

    # LLM Provider Settings (Agno-based)
    llm_provider: str = "openai"  # Options: "openai", "anthropic", "google"
    llm_model_mapping: dict = {
        "openai": "gpt-4o",
        "anthropic": "claude-sonnet-4-20250514",
        "google": "gemini-2.5-flash"
    }
    llm_temperature: float = 0.1
    llm_max_tokens: int = 500

    # API Keys (should be loaded from environment)
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None

    # Agno Agent Settings
    use_agno_framework: bool = True
    agno_markdown_output: bool = False
    agno_show_tool_calls: bool = False

    # Legacy settings (for backward compatibility)
    max_retries: int = 3
    retry_backoff_base: float = 1.0
    retry_backoff_max: float = 10.0

    @validator('llm_provider')
    def validate_llm_provider(cls, v):
        if v not in ["openai", "anthropic", "google"]:
            raise ValueError("llm_provider must be one of: openai, anthropic, google")
        return v

    def get_current_model_id(self) -> str:
        """Get the current model ID based on selected provider."""
        return self.llm_model_mapping.get(self.llm_provider, "gpt-4o")

    def get_api_key_for_provider(self) -> Optional[str]:
        """Get API key for current provider."""
        key_mapping = {
            "openai": self.openai_api_key,
            "anthropic": self.anthropic_api_key,
            "google": self.google_api_key
        }
        return key_mapping.get(self.llm_provider)
```

### Integration Interfaces

#### Existing UseCase Integration

**Note**: Instead of creating new service interfaces, we reuse existing UseCases:

```python
# DetectIntentsUseCase - Extended to handle dual responsibilities
# - When enable_llm_analysis is True: Detects intents and generates summary in a single LLM call
# - Returns IntentDetectionResult with intents and optional summary

class IntentDetectionResult(BaseModel):
    intents: List[IntentType]
    summary: Optional[str] = None  # Only when enable_llm_analysis is True

# SimilaritySearchUseCase - Already implemented
# - Used with processed query text (intent + summary OR original content)
# - Returns List[SimilarityMatch] with scores and categories

# This integrated approach:
# 1. Reduces API calls and costs (single LLM call for related tasks)
# 2. Improves performance (lower latency)
# 3. Maintains logical cohesion (related NLP tasks together)
# 4. Reuses proven business logic and architecture patterns
```

### Testing Framework

#### Unit Test Structure

```python
class TestClassifyComplaintsUseCase:
    """Unit tests for classification orchestrator."""

    @pytest.fixture
    async def use_case(self):
        """Create use case with mocked dependencies."""
        pass

    async def test_successful_classification_high_confidence(self, use_case):
        """Test successful classification with high confidence."""
        pass

    async def test_fallback_classification_low_similarity(self, use_case):
        """Test fallback when similarity scores are low."""
        pass

    async def test_concurrent_batch_processing(self, use_case):
        """Test batch processing with concurrency."""
        pass

    async def test_error_recovery_gemini_failure(self, use_case):
        """Test error recovery when Gemini API fails."""
        pass
```

#### Integration Test Structure

```python
class TestClassificationWorkflowIntegration:
    """Integration tests for complete workflow."""

    async def test_end_to_end_classification_workflow(self):
        """Test complete workflow with real components."""
        pass

    async def test_performance_under_load(self):
        """Test performance with realistic load."""
        pass
```

## Monitoring and Observability

### Metrics Collection

```python
class ClassificationMetrics:
    """Collects classification performance metrics."""

    def __init__(self):
        self.processing_times = []
        self.confidence_distributions = {}
        self.category_assignments = {}
        self.error_counts = {}

    def record_processing_time(self, duration: float):
        """Record individual processing time."""
        pass

    def record_confidence_level(self, confidence: ConfidenceLevel):
        """Record confidence level distribution."""
        pass

    def record_category_assignment(self, category: str):
        """Record category assignment patterns."""
        pass

    def export_metrics(self) -> Dict[str, Any]:
        """Export collected metrics."""
        pass
```

## Security Considerations

### Input Validation

```python
class InputValidator:
    """Validates complaint inputs for security."""

    def validate_complaint_content(self, content: str) -> bool:
        """Validate complaint content for safety."""
        # Check for injection attempts
        # Validate content length and format
        # Sanitize special characters
        pass

    def sanitize_output(self, result: ClassificationResult) -> ClassificationResult:
        """Sanitize output to prevent information disclosure."""
        pass
```

### API Key Management

```python
class SecureConfigManager:
    """Secure management of API keys and secrets."""

    def get_gemini_api_key(self) -> str:
        """Retrieve Gemini API key securely."""
        # Use environment variables or secure vault
        pass

    def rotate_api_keys(self):
        """Handle API key rotation."""
        pass
```

## Deployment Considerations

### Resource Requirements

-   **CPU**: Multi-core recommended for concurrent processing
-   **Memory**: 2GB minimum, 4GB recommended for large batches
-   **Network**: Stable connection for Gemini API and Elasticsearch
-   **Storage**: Minimal, primarily for logging and temporary files

### Environment Configuration

```yaml
# classification_settings.yaml
classification:
    # Feature flags
    enable_llm_analysis: true

    similarity_thresholds:
        primary: 0.75
        fallback: 0.6

    performance:
        max_concurrent: 10
        timeout_seconds: 2

    llm:
        model: "gemini-2.5-flash"
        temperature: 0.1
        max_tokens: 500

    monitoring:
        enable_metrics: true
        log_level: "INFO"
```

---

**Document Version**: 1.5
**Created**: 2025-08-13
**Updated**: 2025-08-13
**Status**: Updated - Agno Framework Integration
**Changes**:

-   Updated workflow to process intent detection before embedding, added feature flags
-   Removed confidence calculator and reasoning generator
-   **v1.2**: Reused existing DetectIntentsUseCase and SimilaritySearchUseCase instead of creating new services
-   Simplified CLASSIFICATION_PROMPT_TEMPLATE, removed numerical thresholds
-   Added conditional content building based on feature flags
-   **v1.3**: Integrated summary generation into DetectIntentsUseCase for efficiency
    -   Single LLM call handles both intent detection and summary generation
    -   Removed separate summary generation logic from ClassifyComplaintsUseCase
    -   Improved API cost efficiency and reduced latency
    -   Enhanced logical cohesion of related NLP tasks
-   **v1.4**: Simplified fallback mechanism for failed classifications
    -   Removed ClassificationFallbackHandler component entirely
    -   Built fallback handling directly into LLMClassificationService
    -   All failed classifications default to "其它" category with failure reason
    -   Simplified error recovery patterns and reduced complexity
    -   Updated prompt template to handle no-match scenarios
-   **v1.5**: Agno Framework Integration and LLM Abstraction
    -   **CONFIRMED**: LLMClassificationService remains as Service (correct architectural decision)
    -   **FIXED**: Removed GeminiEmbedder dependency (embeddings handled in RAG pipeline)
    -   **ADDED**: Abstract LLMProvider interface for multiple LLM support
    -   **IMPLEMENTED**: Agno Agent integration with multi-provider support
        -   OpenAI GPT-4o support
        -   Anthropic Claude Sonnet-4 support
        -   Google Gemini 2.5-flash support
    -   **ENHANCED**: Automatic structured output using Pydantic models
    -   **IMPROVED**: Built-in retry logic and error handling via Agno framework
    -   **UPDATED**: Configuration settings for Agno-based multi-provider setup
    -   **MODERNIZED**: Prompt engineering approach with agent instructions
        **Next Step**: Begin implementation with Agno framework integration
